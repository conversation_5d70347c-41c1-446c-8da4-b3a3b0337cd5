# ECS Deep Diagnose 贡献指南

首先，感谢您考虑为 ECS Deep Diagnose 做出贡献！正是像您这样的人使这个项目成为一个伟大的工具。

### Fork 和创建分支

如果这是您认为可以修复的问题，那么[fork 仓库](https://code.alibaba-inc.com/cloud-ecs-devops/ecs-deep-diagnose/branches)并创建一个具有描述性名称的分支。

我们的分支命名规范如下：

1. 功能开发分支：`feature/功能描述_日期_序号`
   例如：`feature/add_fastapi_20250630_01`

2. 问题修复分支：`fix/问题描述_日期_序号`
   例如：`fix/memory_leak_20250630_01`

### 运行测试套件

确保您能够成功运行测试。我们使用 `pytest` 进行后端测试。

```bash
   # 启动后端服务
   cd src && uv run server.py
   # 运行测试
   cd backend
   pytest tests
```

### 实现您的修复或功能

现在，您已经准备好进行更改了！如果需要帮助，请随时询问；每个人都是从初学者开始的 😸

### 提交 Pull Request

此时，您应该切换回 master 分支，并确保它与存储库的最新上游版本保持同步。

```sh
git remote <NAME_EMAIL>:cloud-ecs-devops/ecs-deep-diagnose.git
git checkout master
git pull upstream master
```

然后从本地 master 副本更新您的功能分支，并推送它！

```sh
git checkout feature/your-feature-name
git rebase master
git push --force-with-lease origin feature/your-feature-name
```


## 保持您的 Pull Request 更新

如果维护者要求您"rebase"您的 PR，他们是说已经有很多代码发生了变化，您需要更新您的分支，使其更容易合并。
