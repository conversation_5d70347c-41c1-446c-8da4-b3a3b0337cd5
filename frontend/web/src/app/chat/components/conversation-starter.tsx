// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";

import { cn } from "~/lib/utils";

import { Welcome } from "./welcome";

const questions = [
  "诊断实例i-5ts7cimdfju19tsvl44u eci-t4nd478m1u72re6phsh8 在2025/05/15的宕机原因?",
  "诊断 i-bp131jfclul2mokez67x 在2025-05-21的 重启原因？ ",
  "对比这2个实例i-5ts7cimdfju19tsvl44u、eci-t4nd478m1u72re6phsh8的对应物理机硬件规格配置差异",
  "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d, i-2vcht9nfld3wrbv28qbs, i-j6c8465htk5rsyb13128, i-j6ch2zf4qfy1rltbql6p, i-j6ccxnd10yb809j28e8o, i-2vcglxct8xop32erzxgt）",
];
export function ConversationStarter({
  className,
  onSend,
}: {
  className?: string;
  onSend?: (message: string) => void;
}) {
  return (
    <div className={cn("flex flex-col items-center", className)}>
      <div className="pointer-events-none fixed inset-0 flex items-center justify-center">
        <Welcome className="pointer-events-auto mb-15 w-[75%] -translate-y-24" />
      </div>
      <ul className="flex flex-wrap">
        {questions.map((question, index) => (
          <motion.li
            key={question}
            className="flex w-1/2 shrink-0 p-2 active:scale-105"
            style={{ transition: "all 0.2s ease-out" }}
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.2,
              delay: index * 0.1 + 0.5,
              ease: "easeOut",
            }}
          >
            <div
              className="bg-card text-muted-foreground cursor-pointer rounded-2xl border px-4 py-4 opacity-75 transition-all duration-300 hover:opacity-100 hover:shadow-md"
              onClick={() => {
                onSend?.(question);
              }}
            >
              {question}
            </div>
          </motion.li>
        ))}
      </ul>
    </div>
  );
}
