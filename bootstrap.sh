#!/bin/bash

# Start both of DeerFlow's backend and web UI server.
# If the user presses Ctrl+C, kill them both.

# 保存项目根目录
PROJECT_ROOT=$(pwd)

if [ "$1" = "--dev" -o "$1" = "-d" -o "$1" = "dev" -o "$1" = "development" ]; then
  echo -e "Starting ECS Deep Diagnose in [DEVELOPMENT] mode...\n"
  
  # 启动后端服务
  echo "Starting backend server..."
  cd "$PROJECT_ROOT/backend/src" && uv run server.py --reload & SERVER_PID=$!
  
  # 检查前端目录是否存在
  if [ -d "$PROJECT_ROOT/frontend/web" ]; then
    echo "Starting frontend server..."
    cd "$PROJECT_ROOT/frontend/web" && pnpm dev & WEB_PID=$!
    trap "kill $SERVER_PID $WEB_PID 2>/dev/null" SIGINT SIGTERM
  else
    echo "Frontend directory not found at $PROJECT_ROOT/frontend/web, running backend only..."
    trap "kill $SERVER_PID 2>/dev/null" SIGINT SIGTERM
  fi
  wait
else
  echo -e "Starting ECS Deep Diagnose in [PRODUCTION] mode...\n"
  
  # 启动后端服务
  echo "Starting backend server..."
  cd "$PROJECT_ROOT/backend/src" && uv run server.py & SERVER_PID=$!
  
  # 检查前端目录是否存在
  if [ -d "$PROJECT_ROOT/frontend/web" ]; then
    echo "Starting frontend server..."
    cd "$PROJECT_ROOT/frontend/web" && pnpm start & WEB_PID=$!
    trap "kill $SERVER_PID $WEB_PID 2>/dev/null" SIGINT SIGTERM
  else
    echo "Frontend directory not found at $PROJECT_ROOT/frontend/web, running backend only..."
    trap "kill $SERVER_PID 2>/dev/null" SIGINT SIGTERM
  fi
  wait
fi
