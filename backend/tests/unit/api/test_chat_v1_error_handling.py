"""
Chat V1 API 错误处理单元测试
"""

from unittest.mock import AsyncMock

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from starlette.middleware.sessions import SessionMiddleware

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.api.route.v1.chat import router
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent, ErrorEvent
from deep_diagnose.domain.chat.models import MessageType

# 创建测试应用并添加必要的中间件
app = FastAPI()
app.add_middleware(SessionMiddleware, secret_key="test-secret-key")
app.include_router(router)

# 创建测试客户端
client = TestClient(app)


# 模拟事件数据
class MockAgentEvent(BaseAgentOutputEvent):
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        # 简单实现parse方法以满足抽象类要求
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1APIErrorHandling:
    """Chat V1 API 错误处理测试"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    def test_chat_v1_invalid_request_data(self):
        """测试 Chat V1 API 无效请求数据"""
        # 测试空问题
        invalid_request = {
            "question": "",  # 空问题，应该违反最小长度验证
            "agent": "ReasoningAgent"
        }
        
        response = client.post("/api/v1/chat", json=invalid_request, headers={"Authorization": "Bearer test-token"})
        # 应该返回422错误（验证错误）
        assert response.status_code == 422
        
        # 测试超长问题
        invalid_request = {
            "question": "x" * 10001,  # 超过最大长度10000
            "agent": "ReasoningAgent"
        }
        
        response = client.post("/api/v1/chat", json=invalid_request, headers={"Authorization": "Bearer test-token"})
        # 应该返回422错误（验证错误）
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_chat_v1_request_validation_exception(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 请求验证异常处理"""
        # 创建一个无效的请求对象来测试验证异常
        class InvalidRequest:
            question = None  # None值应该触发异常
            agent = "ReasoningAgent"
            user_id = mock_user.user_id
            session_id = ""
            additional_info = {}
            
            class MockQuestionType:
                value = "input"
            question_type = MockQuestionType()
        
        request = InvalidRequest()
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 测试服务异常被正确捕获
        with pytest.raises(Exception):
            await chat_v1(request, mock_user, mock_chat_service)
    
    @pytest.mark.asyncio
    async def test_chat_v1_event_generator_exception(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 事件生成器异常处理"""
        # 模拟chat_service.chat在迭代过程中抛出异常
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="正常事件")
            raise Exception("事件生成器内部错误")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "test_session_id"
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id="",
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 读取流式响应
        response_content = ""
        try:
            async for chunk in response.body_iterator:
                if isinstance(chunk, bytes):
                    response_content += chunk.decode('utf-8')
                else:
                    response_content += str(chunk)
        except Exception as e:
            response_content += str(e)
        
        # 验证响应中包含错误事件
        assert "error" in response_content or "事件生成器内部错误" in response_content
    
    @pytest.mark.asyncio
    async def test_chat_v1_service_exception_during_iteration(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 服务异常处理（在迭代过程中）"""
        # 模拟chat_service.chat在开始迭代时就抛出异常
        async def mock_chat_generator(*args, **kwargs):
            raise Exception("服务内部错误")
            yield  # 使函数成为生成器
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "test_session_id"
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id="",
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 读取流式响应
        response_content = ""
        try:
            async for chunk in response.body_iterator:
                if isinstance(chunk, bytes):
                    response_content += chunk.decode('utf-8')
                else:
                    response_content += str(chunk)
        except Exception as e:
            response_content += str(e)
        
        # 验证响应中包含错误事件而不是抛出异常
        assert "error" in response_content
        assert "服务内部错误" in response_content
    
    def test_is_final_event_error_case(self):
        """测试 _is_final_event 函数的错误事件处理"""
        # 导入被测试的函数
        from deep_diagnose.api.route.v1.chat import _is_final_event
        
        # 测试错误事件
        error_event = ErrorEvent(error="test error")
        assert _is_final_event(error_event) == True
    
    def test_is_final_event_with_urls(self):
        """测试 _is_final_event 函数的urls事件处理"""
        # 导入被测试的函数
        from deep_diagnose.api.route.v1.chat import _is_final_event
        
        # 测试带有urls的事件
        url_event = MockAgentEvent(urls=["http://example.com"])
        assert _is_final_event(url_event) == True
    
    def test_is_final_event_with_result(self):
        """测试 _is_final_event 函数的result事件处理"""
        # 导入被测试的函数
        from deep_diagnose.api.route.v1.chat import _is_final_event
        
        # 测试带有result的事件
        result_event = MockAgentEvent(result="test result")
        assert _is_final_event(result_event) == True
    
    def test_is_final_event_normal_event(self):
        """测试 _is_final_event 函数的普通事件处理"""
        # 导入被测试的函数
        from deep_diagnose.api.route.v1.chat import _is_final_event
        
        # 测试普通事件
        normal_event = MockAgentEvent(content="test content")
        assert _is_final_event(normal_event) == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])