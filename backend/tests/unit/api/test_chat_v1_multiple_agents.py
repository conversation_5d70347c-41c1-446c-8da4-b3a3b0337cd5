"""
Chat V1 API 多种Agent类型单元测试
"""

from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import TestClient

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.api.route.v1.chat import router
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import MessageType

# 创建测试客户端
client = TestClient(router)


# 模拟事件数据
class MockAgentEvent(BaseAgentOutputEvent):
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        # 简单实现parse方法以满足抽象类要求
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1APIMultipleAgents:
    """Chat V1 API 多种Agent类型测试"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    @pytest.mark.asyncio
    async def test_chat_v1_reasoning_agent(self, mock_user, mock_chat_service):
        """测试 Chat V1 API ReasoningAgent"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="ReasoningAgent响应1")
            yield MockAgentEvent(content="ReasoningAgent响应2", result="最终推理结果")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "reasoning_session_id"
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",  # 使用ReasoningAgent
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            response_content += chunk
        
        # 验证响应中包含正确的agent类型
        assert "ReasoningAgent" in response_content
        assert "reasoning_session_id" in response_content
    
    @pytest.mark.asyncio
    async def test_chat_v1_inspect_agent(self, mock_user, mock_chat_service):
        """测试 Chat V1 API InspectAgent"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="InspectAgent响应1")
            yield MockAgentEvent(content="InspectAgent响应2", urls=["http://example.com/report"])
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "inspect_session_id"
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="InspectAgent",  # 使用InspectAgent
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            response_content += chunk
        
        # 验证响应中包含正确的agent类型
        assert "InspectAgent" in response_content
        assert "inspect_session_id" in response_content
    
    @pytest.mark.asyncio
    async def test_chat_v1_custom_agent(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 自定义Agent"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="CustomAgent响应1")
            yield MockAgentEvent(content="CustomAgent响应2", result="自定义结果")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "custom_session_id"
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="CustomAgent",  # 使用自定义Agent
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            response_content += chunk
        
        # 验证响应中包含正确的agent类型
        assert "CustomAgent" in response_content
        assert "custom_session_id" in response_content
    
    @pytest.mark.asyncio
    async def test_chat_v1_agent_fallback(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 默认Agent"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="默认Agent响应1")
            yield MockAgentEvent(content="默认Agent响应2", result="默认结果")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "default_session_id"
        
        # 创建 Chat V1 请求（不指定agent，应该使用默认值）
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",  # 默认值
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            response_content += chunk
        
        # 验证响应中包含默认agent类型
        assert "ReasoningAgent" in response_content
        assert "default_session_id" in response_content


if __name__ == "__main__":
    pytest.main([__file__, "-v"])