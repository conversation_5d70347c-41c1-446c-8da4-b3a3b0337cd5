"""
Chat V1 API 会话管理单元测试
"""

from datetime import datetime
from unittest.mock import AsyncMock

import pytest
from fastapi import HTTPException
from fastapi.testclient import TestClient

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.api.route.v1.chat import router
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import MessageType

# 创建测试客户端
client = TestClient(router)


# 模拟事件数据
class MockAgentEvent(BaseAgentOutputEvent):
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        # 简单实现parse方法以满足抽象类要求
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1APISessionManagement:
    """Chat V1 API 会话管理测试"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    @pytest.mark.asyncio
    async def test_chat_v1_new_session_creation(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 新会话创建"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="测试响应1")
            yield MockAgentEvent(content="测试响应2", result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "newly_created_session_id"
        
        # 创建 Chat V1 请求（不提供session_id，应该创建新会话）
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,  # 不提供session_id
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            response_content += chunk
        
        # 验证响应中包含新创建的session_id
        assert "newly_created_session_id" in response_content
        assert "test_session_id" not in response_content  # 应该使用实际的session_id
    
    @pytest.mark.asyncio
    async def test_chat_v1_existing_session_usage(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 使用现有会话"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="测试响应1")
            yield MockAgentEvent(content="测试响应2", result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        mock_chat_service.current_session_id = "existing_session_id"
        
        # 创建 Chat V1 请求（提供session_id，应该使用现有会话）
        request = ChatRequestV1(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id="provided_session_id",  # 提供session_id
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
        
        # 读取流式响应
        response_content = ""
        async for chunk in response.body_iterator:
            if isinstance(chunk, bytes):
                response_content += chunk.decode('utf-8')
            else:
                response_content += str(chunk)
        
        # 验证响应中包含实际的session_id（服务端生成的）
        assert "existing_session_id" in response_content
    
    @pytest.mark.asyncio
    async def test_get_user_sessions_success(self, mock_user, mock_chat_service):
        """测试获取用户会话列表成功"""
        # 模拟会话数据
        mock_sessions = [
            {
                "session_id": "session_1",
                "title": "测试会话1",
                "gmt_create": datetime.now()
            },
            {
                "session_id": "session_2",
                "title": "测试会话2",
                "gmt_create": datetime.now()
            }
        ]
        
        mock_chat_service.get_user_sessions.return_value = mock_sessions
        
        # 导入实际的get_user_sessions函数来测试
        from deep_diagnose.api.route.v1.chat import get_user_sessions
        
        # 调用接口
        response = await get_user_sessions("test_user", mock_user, mock_chat_service)
        
        # 验证响应数据
        assert response.total == 2
        assert len(response.sessions) == 2
        assert response.sessions[0].session_id == "session_1"
        assert response.sessions[1].session_id == "session_2"
    
    @pytest.mark.asyncio
    async def test_get_user_sessions_empty(self, mock_user, mock_chat_service):
        """测试获取用户会话列表为空"""
        # 模拟空会话数据
        mock_chat_service.get_user_sessions.return_value = []
        
        # 导入实际的get_user_sessions函数来测试
        from deep_diagnose.api.route.v1.chat import get_user_sessions
        
        # 调用接口
        response = await get_user_sessions("test_user", mock_user, mock_chat_service)
        
        # 验证响应数据
        assert response.total == 0
        assert len(response.sessions) == 0
    
    @pytest.mark.asyncio
    async def test_get_user_sessions_service_exception(self, mock_user, mock_chat_service):
        """测试获取用户会话列表服务异常"""
        # 模拟服务异常
        mock_chat_service.get_user_sessions.side_effect = Exception("数据库错误")
        
        # 导入实际的get_user_sessions函数来测试
        from deep_diagnose.api.route.v1.chat import get_user_sessions
        
        # 测试服务异常被正确捕获并转换为HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await get_user_sessions("test_user", mock_user, mock_chat_service)
        
        assert exc_info.value.status_code == 500
        assert "获取用户会话列表失败" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_get_session_messages_success(self, mock_user, mock_chat_service):
        """测试获取会话消息列表成功"""
        # 模拟消息数据
        mock_messages = [
            {
                "message": "用户消息1",
                "agent": "ReasoningAgent",
                "message_type": "human_query",
                "gmt_create": datetime.now()
            },
            {
                "message": "AI响应1",
                "agent": "ReasoningAgent",
                "message_type": "ai_response",
                "gmt_create": datetime.now()
            }
        ]
        
        mock_chat_service.get_session_messages.return_value = mock_messages
        
        # 导入实际的get_session_messages函数来测试
        from deep_diagnose.api.route.v1.chat import get_session_messages
        
        # 调用接口
        response = await get_session_messages("test_session_id", mock_user, mock_chat_service)
        
        # 验证响应数据
        assert response.total == 2
        assert len(response.messages) == 2
        assert response.messages[0].message == "用户消息1"
        assert response.messages[0].agent == "ReasoningAgent"
        assert response.messages[1].message == "AI响应1"
        assert response.messages[1].agent == "ReasoningAgent"
    
    @pytest.mark.asyncio
    async def test_get_session_messages_empty(self, mock_user, mock_chat_service):
        """测试获取会话消息列表为空"""
        # 模拟空消息数据
        mock_chat_service.get_session_messages.return_value = []
        
        # 导入实际的get_session_messages函数来测试
        from deep_diagnose.api.route.v1.chat import get_session_messages
        
        # 调用接口
        response = await get_session_messages("test_session_id", mock_user, mock_chat_service)
        
        # 验证响应数据
        assert response.total == 0
        assert len(response.messages) == 0
    
    @pytest.mark.asyncio
    async def test_get_session_messages_service_exception(self, mock_user, mock_chat_service):
        """测试获取会话消息列表服务异常"""
        # 模拟服务异常
        mock_chat_service.get_session_messages.side_effect = Exception("数据库错误")
        
        # 导入实际的get_session_messages函数来测试
        from deep_diagnose.api.route.v1.chat import get_session_messages
        
        # 测试服务异常被正确捕获并转换为HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await get_session_messages("test_session_id", mock_user, mock_chat_service)
        
        assert exc_info.value.status_code == 500
        assert "获取会话消息列表失败" in str(exc_info.value.detail)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])