"""
Chat V1 API 单元测试
"""

from unittest.mock import AsyncMock

import pytest

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent, ErrorEvent
from deep_diagnose.domain.chat.models import MessageType


# 模拟事件数据
class MockAgentEvent(BaseAgentOutputEvent):
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        # 简单实现parse方法以满足抽象类要求
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1API:
    """Chat V1 API 单元测试"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    @pytest.mark.asyncio
    async def test_chat_v1_simple(self, mock_user, mock_chat_service):
        """简化测试 Chat V1 API 功能"""
        
        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question="测试问题",
            agent="InspectAgent",
            user_id=mock_user.user_id,
            session_id="",
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 验证请求对象创建成功
        assert request.question == "测试问题"
        assert request.agent == "InspectAgent"
        assert request.user_id == mock_user.user_id
    
    @pytest.mark.asyncio
    async def test_chat_v1_error_handling_simple(self, mock_user, mock_chat_service):  # pylint: disable=unused-argument
        """简化测试 Chat V1 API 错误处理"""
        
        # 创建带有错误的模拟事件
        error_event = MockAgentEvent(error="测试错误")
        
        # 验证错误事件正确创建
        assert error_event.error == "测试错误"
        assert "error" in error_event.to_sse_format()
    
    def test_is_final_event(self):
        """测试 _is_final_event 函数"""
        # 导入被测试的函数
        from deep_diagnose.api.route.v1.chat import _is_final_event
        
        # 测试错误事件
        error_event = ErrorEvent(error="test error")
        assert _is_final_event(error_event) == True
        
        # 测试带有urls的事件
        url_event = MockAgentEvent(urls=["http://example.com"])
        assert _is_final_event(url_event) == True
        
        # 测试带有result的事件
        result_event = MockAgentEvent(result="test result")
        assert _is_final_event(result_event) == True
        
        # 测试普通事件
        normal_event = MockAgentEvent(content="test content")
        assert _is_final_event(normal_event) == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])