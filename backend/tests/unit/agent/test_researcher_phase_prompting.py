import pytest
from langchain_core.messages import HumanMessage, ToolMessage
from deep_diagnose.core.reasoning.agents.react_agent_factory import create_agent_with_tools
from deep_diagnose.common.config.core.configuration import Configuration


@pytest.mark.asyncio
async def test_generate_prompt_phase_switch(monkeypatch):
    # 构造一个假的 LLM，记录传入的 prompt messages
    class DummyLLM:
        def __init__(self):
            self.parallel_tool_calls = True
            self.bound_kwargs = {}
        def bind(self, **kwargs):
            self.bound_kwargs.update(kwargs)
            return self
    
    # 打桩 get_llm_by_type 返回 DummyLLM
    import deep_diagnose.core.reasoning.agents.react_agent_factory as utils_mod
    monkeypatch.setattr(utils_mod, 'get_llm_by_type', lambda llm_type: DummyLLM())

    # 捕获传给 create_react_agent 的 prompt 可调用，并执行它
    captured_prompt_fn = {}
    def fake_create_react_agent(name, model, tools, prompt):
        captured_prompt_fn['fn'] = prompt
        return object()
    monkeypatch.setattr(utils_mod, 'create_react_agent', fake_create_react_agent)

    # 调用工厂创建 agent（不会真正调用 LLM）
    agent = await create_agent_with_tools('researcher', tools=[], config={"configurable": {}})
    assert agent is not None

    # 1) 无 ToolMessage 时应选择 select 模板
    state = {
        'messages': [HumanMessage(content='检查实例 i-123 在 01:00-02:00 是否重启过')],
        'locale': 'zh-CN',
    }
    prompt_msgs = await captured_prompt_fn['fn'](state)
    assert isinstance(prompt_msgs, list) and len(prompt_msgs) >= 1
    assert prompt_msgs[0]['role'] == 'system'
    assert '工具选择' in prompt_msgs[0]['content'] or '工具选择与调用阶段' in prompt_msgs[0]['content']

    # 2) 有 ToolMessage 时应选择 final 模板，并包含近期证据
    state2 = {
        'messages': [
            HumanMessage(content='上面的查询继续'),
            ToolMessage(content='{"data": []}', tool_call_id='tc1'),
        ],
        'locale': 'zh-CN',
    }
    prompt_msgs2 = await captured_prompt_fn['fn'](state2)
    assert prompt_msgs2[0]['role'] == 'system'
    assert '结果分析' in prompt_msgs2[0]['content'] or '答复阶段' in prompt_msgs2[0]['content']
    assert 'tc1' in prompt_msgs2[0]['content']  # recent_tool_evidence 已注入
