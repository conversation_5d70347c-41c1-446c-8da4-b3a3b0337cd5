import requests
import json

endpoint = "http://localhost:8000"
auth_api = "/api/token"
task_id = "ca31868c-234b-4946-9759-de8aaf28efea"

generate_api = "/api/v1/tasks/" + task_id
auth_body = {
    "access_key": "admin",
    "secret_key": "admin",
    "token_lifetime_minutes": 60
}
headers = {"Content-Type": "application/json"}
token = requests.post(endpoint + auth_api, json=auth_body, headers=headers)
authorization = f"Bearer {json.loads(token.content).get('access_token')}"
chat_body = {
    "task_id": task_id
}
headers = {"Authorization": authorization, "Content-Type": "application/json"}

with requests.get(
        endpoint + generate_api +"?task_id=" + task_id,
        headers=headers,
        json=chat_body
) as response:
    # 确保请求成功
    response.raise_for_status()
    data = response.json()
    print(data)
