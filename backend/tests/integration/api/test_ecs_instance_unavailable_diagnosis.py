"""
ECS实例不可用问题诊断API集成测试

测试调用 /api/chat/stream API 进行批量ECS实例不可用问题排查
"""

import asyncio
import json
import uuid
import httpx
import pytest
import os
from datetime import datetime


class TestECSInstanceUnavailableDiagnosis:
    """ECS实例不可用问题诊断测试类"""

    @pytest.mark.asyncio
    async def test_batch_ecs_instance_unavailable_diagnosis(self):
        """测试批量ECS实例不可用问题诊断"""
        
        # 设置超时时间（诊断可能需要较长时间）
        timeout = httpx.Timeout(300.0)  # 5分钟超时
        
        # 准备SSE事件保存文件路径
        sse_events_file = os.path.join(os.path.dirname(__file__), "api_sse_event.txt")
        
        async with httpx.AsyncClient(
            base_url="http://localhost:8000/",
            timeout=timeout
        ) as client:
            
            # 第一步：获取访问令牌
            print("🔐 正在获取访问令牌...")
            token_request_data = {
                "access_key": "admin",
                "secret_key": "admin",
                "token_lifetime_minutes": 60
            }

            token_response = await client.post(
                "/api/token",
                json=token_request_data
            )
            
            assert token_response.status_code == 200, f"Token API failed: {token_response.status_code}"
            token_content = json.loads(token_response.content)
            assert token_content is not None
            assert "access_token" in token_content
            
            access_token = token_content.get("access_token")
            print(f"✅ 成功获取访问令牌")

            # 第二步：构建诊断请求
            print("🔍 正在构建ECS实例诊断请求...")
            diagnosis_request_data = {
                "messages": [
                    {
                        "role": "user",
                        "content": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d, i-2vcht9nfld3wrbv28qbs, i-j6c8465htk5rsyb13128, i-j6ch2zf4qfy1rltbql6p, i-j6ccxnd10yb809j28e8o, i-2vcglxct8xop32erzxgt）"
                    }
                ],
                "thread_id": str(uuid.uuid4()),  # 使用随机生成的thread_id
                "auto_accepted_plan": False,
                "enable_background_investigation": False,
                "max_plan_iterations": 1,
                "max_step_num": 5,  # 增加步骤数以支持复杂诊断
                "max_search_results": 5  # 增加搜索结果数
            }

            print(f"📋 请求参数: thread_id={diagnosis_request_data['thread_id']}")
            print(f"📋 实例数量: 12个")
            print(f"📋 时间范围: 2025年6月26日凌晨1-4点")

            # 第三步：发送诊断请求
            print("🚀 正在发送诊断请求...")
            diagnosis_response = await client.post(
                "/api/chat/stream",
                json=diagnosis_request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}"
                }
            )

            # 验证响应状态码
            assert diagnosis_response.status_code == 200, f"Chat stream API failed: {diagnosis_response.status_code}"
            print("✅ 诊断请求发送成功，开始接收SSE事件...")

            # 第四步：接收并保存SSE事件
            sse_events = []
            event_count = 0
            start_time = datetime.now()
            
            # 确保目录存在
            sse_dir = os.path.dirname(sse_events_file)
            if sse_dir:  # 只有当目录路径不为空时才创建
                os.makedirs(sse_dir, exist_ok=True)
            
            try:
                async for chunk in diagnosis_response.aiter_text():
                    if chunk.strip():  # 忽略空行
                        event_count += 1
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        
                        # 添加时间戳到事件
                        timestamped_event = f"[{timestamp}] {chunk}"
                        sse_events.append(timestamped_event)
                        
                        # 实时打印关键事件（避免输出过多）
                        if event_count % 50 == 0:
                            print(f"📡 已接收 {event_count} 个SSE事件...")
                        
                        # 检查是否包含关键诊断信息
                        if any(keyword in chunk.lower() for keyword in [
                            "诊断", "实例", "不可用", "变更", "故障", "异常", "错误"
                        ]):
                            print(f"🔍 发现关键诊断信息: {chunk[:100]}...")

            except Exception as e:
                print(f"⚠️  SSE流接收过程中出现异常: {e}")
                # 即使出现异常，也要保存已接收的事件
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"📊 SSE事件接收完成:")
            print(f"   - 总事件数: {event_count}")
            print(f"   - 耗时: {duration:.2f}秒")
            print(f"   - 平均事件频率: {event_count/duration:.2f}事件/秒")

            # 第五步：保存SSE事件到文件
            print(f"💾 正在保存SSE事件到文件: {sse_events_file}")
            
            with open(sse_events_file, 'w', encoding='utf-8') as f:
                # 写入文件头信息
                f.write(f"# ECS实例不可用问题诊断SSE事件记录\n")
                f.write(f"# 测试时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 请求ID: {diagnosis_request_data['thread_id']}\n")
                f.write(f"# 总事件数: {event_count}\n")
                f.write(f"# 诊断耗时: {duration:.2f}秒\n")
                f.write(f"# 实例列表: i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d, i-2vcht9nfld3wrbv28qbs, i-j6c8465htk5rsyb13128, i-j6ch2zf4qfy1rltbql6p, i-j6ccxnd10yb809j28e8o, i-2vcglxct8xop32erzxgt\n")
                f.write(f"# 问题时间: 2025年6月26日凌晨1-4点\n")
                f.write("=" * 80 + "\n\n")
                
                # 写入所有SSE事件
                for event in sse_events:
                    f.write(event)
                    if not event.endswith('\n'):
                        f.write('\n')

            print(f"✅ SSE事件已成功保存到: {sse_events_file}")

            # 第六步：验证响应内容
            full_response = "".join(sse_events)
            
            # 基本验证
            assert len(sse_events) > 0, "应该接收到至少一个SSE事件"
            assert "data:" in full_response, "SSE响应应该包含data字段"
            
            # 诊断相关验证
            response_lower = full_response.lower()
            
            # 验证包含实例ID
            instance_ids = [
                "i-t4n4vky24zw2w1qnqoyf", "i-t4n74bsfzx58x0lj4qbh", "i-t4na3cc0c9mimcw9667x",
                "i-j6ch2zf4qfy1rltbql6r", "i-2vc5alcmxz75rw8aol4g", "i-2vc0zliaw8ilg744cdrq",
                "i-2vc6qv34j96hkmcwms5d", "i-2vcht9nfld3wrbv28qbs", "i-j6c8465htk5rsyb13128",
                "i-j6ch2zf4qfy1rltbql6p", "i-j6ccxnd10yb809j28e8o", "i-2vcglxct8xop32erzxgt"
            ]
            
            found_instances = sum(1 for instance_id in instance_ids if instance_id in response_lower)
            print(f"🔍 在响应中找到 {found_instances}/{len(instance_ids)} 个实例ID")
            
            # 验证包含诊断相关关键词
            diagnostic_keywords = [
                "诊断", "实例", "不可用", "排查", "问题", "原因", "分析"
            ]
            
            found_keywords = [kw for kw in diagnostic_keywords if kw in response_lower]
            print(f"🔍 在响应中找到诊断关键词: {found_keywords}")
            
            # 验证包含时间相关信息
            time_keywords = ["2025", "6月", "26日", "凌晨", "1-4点"]
            found_time_keywords = [kw for kw in time_keywords if kw in response_lower]
            print(f"🔍 在响应中找到时间关键词: {found_time_keywords}")
            
            # 断言验证
            assert found_instances >= 3, f"应该在响应中找到至少3个实例ID，实际找到{found_instances}个"
            assert len(found_keywords) >= 3, f"应该包含至少3个诊断关键词，实际找到{found_keywords}"
            
            print("🎉 ECS实例不可用问题诊断测试完成！")
            print(f"📄 详细SSE事件已保存到: {os.path.abspath(sse_events_file)}")

    @pytest.mark.asyncio
    async def test_sse_event_file_accessibility(self):
        """测试SSE事件文件的可访问性"""
        sse_events_file = os.path.join(os.path.dirname(__file__), "api_sse_event.txt")
        
        # 检查文件是否存在
        if os.path.exists(sse_events_file):
            # 验证文件可读
            with open(sse_events_file, 'r', encoding='utf-8') as f:
                content = f.read()
                assert len(content) > 0, "SSE事件文件不应为空"
                assert "ECS实例不可用问题诊断SSE事件记录" in content, "文件应包含正确的头信息"
            
            print(f"✅ SSE事件文件验证通过: {sse_events_file}")
        else:
            print(f"⚠️  SSE事件文件不存在，请先运行主要诊断测试: {sse_events_file}")


if __name__ == "__main__":
    # 可以直接运行此文件进行测试
    asyncio.run(TestECSInstanceUnavailableDiagnosis().test_batch_ecs_instance_unavailable_diagnosis())