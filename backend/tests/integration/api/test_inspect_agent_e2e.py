"""
Inspect Agent 端到端测试

通过 Chat V1 API 接口测试 Inspect Agent 的完整功能
"""

import asyncio
import json
import logging
from typing import List, Dict, Any
from uuid import uuid4

import pytest

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import MessageType
from deep_diagnose.services.chat.chat_service import create_chat_service

logger = logging.getLogger(__name__)


class InspectAgentE2ETester:
    """Inspect Agent 端到端测试器"""

    def __init__(self):
        self.test_cases = [
            {
                "machine_id": "i-bp1fz27ong6p6w693vn5",
                "description": "ECS Instance ID 测试",
                "start_time": "2025-07-28 00:00:00",
                "end_time": "2025-07-30 23:59:59",
            },
            {
                "machine_id": "***********",
                "description": "NC IP Address 测试",
                "start_time": "2025-07-28 00:00:00",
                "end_time": "2025-07-30 23:59:59",
            },
        ]

    def _is_final_event(self, agent_event: BaseAgentOutputEvent) -> bool:
        """判断是否为最终事件（复用 chat.py 中的逻辑）"""
        from deep_diagnose.core.events.base_event import ErrorEvent

        # 错误事件是最终事件
        if isinstance(agent_event, ErrorEvent):
            return True

        # 对于推理事件，如果有urls内容，认为是最终事件
        if hasattr(agent_event, "urls") and getattr(agent_event, "urls", None):
            urls = getattr(agent_event, "urls")
            if isinstance(urls, list) and len(urls) > 0:
                return True

        # 对于推理事件，如果有result内容且非空，也可能是最终事件
        if hasattr(agent_event, "result") and getattr(agent_event, "result", "").strip():
            return True

        return False

    async def _ensure_clean_environment(self):
        """确保测试环境的清洁，避免事件循环冲突"""
        import asyncio
        import gc
        from deep_diagnose.data.database import db_manager

        # 强制垃圾回收，清理可能残留的异步对象
        gc.collect()

        # 关闭现有的数据库连接以避免事件循环冲突
        try:
            await db_manager.close_database()
        except Exception:
            pass

        # 确保当前事件循环是干净的
        try:
            # 获取当前事件循环
            loop = asyncio.get_running_loop()
            # 等待所有待处理的任务完成
            pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            if pending_tasks:
                await asyncio.sleep(0.1)  # 给任务一些时间完成
        except Exception:
            pass

        # 重新初始化数据库连接
        try:
            await db_manager.init_database()
        except Exception:
            pass

    async def run_single_test_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行单个测试用例

        Args:
            test_case: 测试用例配置

        Returns:
            Dict[str, Any]: 测试结果统计
        """
        # 确保测试环境清洁，避免事件循环冲突
        await self._ensure_clean_environment()

        logger.info(f"开始测试用例: {test_case['description']}")
        logger.info(f"Machine ID: {test_case['machine_id']}")
        logger.info(f"Time Range: {test_case['start_time']} ~ {test_case['end_time']}")

        # 模拟用户信息
        mock_user = UserModel(
            user_id="test_user_e2e", user_name="test_user", user_type="test_user_type", pop_user="test_pop_user", access_key="test_access_key"
        )

        # 在异步方法内部创建聊天服务实例，确保在正确的事件循环中初始化
        try:
            chat_service = create_chat_service()
        except Exception as e:
            logger.error(f"Failed to create chat service: {e}")
            return {
                "test_case": test_case,
                "request_id": "failed_to_create_service",
                "event_count": 0,
                "final_status": "error",
                "final_response": None,
                "overview_length": 0,
                "recommendations_count": 0,
                "error": f"Service creation failed: {str(e)}",
            }

        # 构建问题，包含机器ID和时间范围信息
        question = f"请分析机器 {test_case['machine_id']} 在 {test_case['start_time']} 到 {test_case['end_time']} 期间的状态"

        # 构建附加信息
        additional_info = {
            "machine_id": test_case["machine_id"],
            "start_time": test_case["start_time"],
            "end_time": test_case["end_time"],
        }

        # 创建 Chat V1 请求
        request = ChatRequestV1(
            question=question,
            agent="InspectAgent",  # 使用 InspectAgent
            user_id=mock_user.user_id,
            session_id="",
            additional_info=additional_info,
            question_type=MessageType.HUMAN_QUERY,
        )

        # 生成请求ID
        request_id = f"req_{uuid4()}"

        logger.info(f"发送请求:")
        logger.info(f"   - 问题: {question}")
        logger.info(f"   - Agent: {request.agent}")
        logger.info(f"   - Session ID: {request.session_id}")
        logger.info(f"   - 附加信息: {additional_info}")

        # 测试结果统计
        test_result = {
            "test_case": test_case,
            "request_id": request_id,
            "event_count": 0,
            "final_status": None,
            "final_response": None,
            "overview_length": 0,
            "recommendations_count": 0,
            "error": None,
        }

        try:
            logger.info("开始 Chat V1 API 事件流处理...")

            # 模拟 chat_v1 接口的内部逻辑
            actual_session_id = None
            last_data_len = 0

            # 创建一个异步生成器处理函数
            async def process_chat_events():
                async for agent_event in chat_service.chat(
                    question=request.question,
                    user_id=request.user_id or "anonymous",
                    agent=request.agent,
                    session_id=request.session_id,
                    messages=None,
                    request_id=request_id,
                    thread_id=request_id,
                    question_type=request.question_type.value,
                    additional_info=request.additional_info,
                ):
                    nonlocal actual_session_id, last_data_len

                    # 从 chat_service 获取实际的 session_id
                    if actual_session_id is None and hasattr(chat_service, "current_session_id"):
                        actual_session_id = chat_service.current_session_id

                    # 确保是 BaseAgentOutputEvent 类型
                    if isinstance(agent_event, BaseAgentOutputEvent):
                        test_result["event_count"] += 1

                        # 构建标准API响应格式（模拟 chat_v1 接口返回）
                        api_response = {
                            "request_id": request_id,
                            "session_id": actual_session_id or request.session_id,
                            "agent": request.agent,
                            "data": agent_event.to_sse_format(),
                            "status": "processing",
                        }

                        # 判断是否为最终状态
                        if hasattr(agent_event, "error") and getattr(agent_event, "error", None):
                            api_response["status"] = "error"
                        elif self._is_final_event(agent_event):
                            api_response["status"] = "completed"

                        # 解析数据内容并更新统计
                        data = api_response["data"]
                        current_data_len = len(str(data))

                        # 记录数据更新
                        if current_data_len > last_data_len:
                            increase = current_data_len - last_data_len
                            logger.debug(f"数据更新 (+{increase} 字符)")
                            last_data_len = current_data_len

                        # 保存最终响应
                        test_result["final_response"] = api_response
                        test_result["final_status"] = api_response["status"]

                        # 解析具体的数据内容
                        if isinstance(data, str):
                            try:
                                parsed_data = json.loads(data)
                                if isinstance(parsed_data, dict):
                                    if "overview" in parsed_data and parsed_data["overview"]:
                                        test_result["overview_length"] = len(parsed_data["overview"])
                                    if "recommendations" in parsed_data and parsed_data["recommendations"]:
                                        test_result["recommendations_count"] = len(parsed_data["recommendations"])
                            except json.JSONDecodeError:
                                pass

                        # 任务完成时结束循环
                        if api_response["status"] in ["completed", "error"]:
                            logger.info(f"Chat V1 API 任务完成!")
                            logger.info(f"最终统计:")
                            logger.info(f"   - 请求ID: {api_response['request_id']}")
                            logger.info(f"   - 会话ID: {api_response['session_id']}")
                            logger.info(f"   - Agent: {api_response['agent']}")
                            logger.info(f"   - 最终状态: {api_response['status']}")
                            logger.info(f"   - 处理事件总数: {test_result['event_count']} 个")
                            break

                    else:
                        # 如果不是预期类型，记录警告
                        logger.warning(f"意外的事件类型: {type(agent_event)}")

            # 使用超时处理
            import asyncio

            await asyncio.wait_for(process_chat_events(), timeout=300.0)  # 5分钟超时

            logger.info(f"Chat V1 API 测试用例完成，共处理 {test_result['event_count']} 个事件")

        except asyncio.TimeoutError:
            logger.error(f"Chat V1 API 测试超时（5分钟）")
            test_result["error"] = "Test timeout after 5 minutes"
            test_result["final_status"] = "timeout"
        except Exception as e:
            logger.error(f"Chat V1 API 测试用例执行失败: {str(e)}", exc_info=True)
            test_result["error"] = str(e)
            test_result["final_status"] = "error"

        return test_result

    async def run_all_test_cases(self) -> List[Dict[str, Any]]:
        """
        运行所有测试用例

        Returns:
            List[Dict[str, Any]]: 所有测试结果
        """
        logger.info("🚀 Starting Inspect Agent End-to-End Test via Chat V1 API...")
        logger.info("📋 测试范围: 通过 Chat V1 API 接口调用 InspectAgent")

        results = []

        for i, test_case in enumerate(self.test_cases, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 测试用例 {i}: {test_case['description']}")
            logger.info("=" * 60)

            result = await self.run_single_test_case(test_case)
            results.append(result)

        logger.info(f"\n🏁 Chat V1 API 端到端测试执行完成!")
        return results


@pytest.mark.asyncio
@pytest.mark.skip(reason="FVT测试，单元测试跳过")
async def test_inspect_agent_with_ecs_instance():
    """测试 Inspect Agent 处理 ECS Instance ID"""
    tester = InspectAgentE2ETester()

    # 只运行第一个测试用例（ECS Instance ID）
    test_case = tester.test_cases[0]

    result = await tester.run_single_test_case(test_case)

    # 验证测试结果
    assert result["error"] is None, f"测试失败: {result['error']}"
    assert result["event_count"] > 0, "应该接收到至少一个事件"
    assert result["final_status"] in ["completed", "processing"], f"最终状态应该是 completed 或 processing，实际: {result['final_status']}"

    # 如果任务完成，验证内容
    if result["final_status"] == "completed":
        assert result["overview_length"] > 0, "应该生成概览内容"

    logger.info(f"✅ ECS Instance ID 测试通过: 事件数={result['event_count']}, 状态={result['final_status']}")


@pytest.mark.asyncio
@pytest.mark.skip(reason="FVT测试，单元测试跳过")
async def test_inspect_agent_with_nc_ip():
    """测试 Inspect Agent 处理 NC IP Address"""
    tester = InspectAgentE2ETester()

    # 只运行第二个测试用例（NC IP Address）
    test_case = tester.test_cases[1]
    result = await tester.run_single_test_case(test_case)

    # 验证测试结果
    assert result["error"] is None, f"测试失败: {result['error']}"
    assert result["event_count"] > 0, "应该接收到至少一个事件"
    assert result["final_status"] in ["completed", "processing"], f"最终状态应该是 completed 或 processing，实际: {result['final_status']}"

    # 如果任务完成，验证内容
    if result["final_status"] == "completed":
        assert result["overview_length"] > 0, "应该生成概览内容"

    logger.info(f"✅ NC IP Address 测试通过: 事件数={result['event_count']}, 状态={result['final_status']}")


@pytest.mark.asyncio
@pytest.mark.skip(reason="FVT测试，单元测试跳过")
async def test_inspect_agent_all_cases():
    """测试 Inspect Agent 的所有测试用例"""
    tester = InspectAgentE2ETester()
    results = await tester.run_all_test_cases()

    # 验证所有测试结果
    assert len(results) == len(tester.test_cases), "应该完成所有测试用例"

    success_count = 0
    for i, result in enumerate(results):
        logger.info(f"测试用例 {i+1} 结果: 事件数={result['event_count']}, 状态={result['final_status']}, 错误={result['error']}")

        if result["error"] is None and result["event_count"] > 0:
            success_count += 1

    logger.info(f"✅ 总体测试结果: {success_count}/{len(results)} 个测试用例成功")

    # 至少要有一个测试用例成功
    assert success_count > 0, "至少应该有一个测试用例成功"


if __name__ == "__main__":
    # 允许直接运行此文件进行手动测试
    async def main():
        tester = InspectAgentE2ETester()
        results = await tester.run_all_test_cases()

        print(f"\n📊 测试结果总结:")
        print(f"{'='*80}")

        for i, result in enumerate(results, 1):
            test_case = result["test_case"]
            print(f"\n测试用例 {i}: {test_case['description']}")
            print(f"   Machine ID: {test_case['machine_id']}")
            print(f"   请求ID: {result['request_id']}")
            print(f"   事件数: {result['event_count']}")
            print(f"   最终状态: {result['final_status']}")
            print(f"   概览长度: {result['overview_length']} 字符")
            print(f"   推荐数量: {result['recommendations_count']} 个")
            if result["error"]:
                print(f"   错误: {result['error']}")
            else:
                print(f"   ✅ 测试成功")

    asyncio.run(main())
