async def test_task_service_crud_flow():
    """测试任务服务功能"""
    from deep_diagnose.services.task.task_service import task_service
    from deep_diagnose.common.models.task_models import TaskStatus

    # 测试创建任务
    task_id = await task_service.create_task(
        agent="DiagnoseAgent",
        question="测试问题：请分析系统状态",
        user_id="test_user"
    )
    assert task_id is not None

    # 测试获取任务
    task_info = await task_service.get_task(task_id)
    assert task_info is not None
    assert task_info.status == TaskStatus.PENDING

    # 再次获取任务验证状态更新
    task_info = await task_service.get_task(task_id)
    assert task_info is not None
    assert task_info.status == TaskStatus.PROCESSING

    # 测试完成任务
    await task_service.complete_task_success(
        task_id,
        result="## 测试结果\n\n这是一个测试结果。",
        detail="## 执行过程\n\n### 步骤 1\n\n测试步骤执行成功。"
    )

    # 最终验证
    task_info = await task_service.get_task(task_id)
    assert task_info is not None
    assert task_info.status == TaskStatus.SUCCESS
    assert task_info.data is not None
    assert task_info.data.result is not None
    assert task_info.data.detail is not None


async def test_redis_connection():
    """测试Redis连接"""
    from deep_diagnose.storage.redis_client import RedisClient

    redis_client = RedisClient()

    # 测试设置和获取
    test_key = "test_integration_key"
    test_value = "test_integration_value"

    redis_client.set_cache(test_key, test_value, ttl_seconds=60)

    retrieved_value = redis_client.get_cache(test_key)
    assert retrieved_value == test_value

    # 清理测试数据
    redis_client.delete_cache(test_key)
