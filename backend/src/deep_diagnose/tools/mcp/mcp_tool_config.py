"""
MCP配置提供器

职责：
- 提供标准化的MCP服务器配置
- 处理服务器名称映射
- 管理启用工具列表
"""

from typing import Dict, List, Any, Optional

from deep_diagnose.common.config import get_config
from deep_diagnose.common.config.core.base_config import DotDict


class MCPToolConfig:
    """MCP配置提供器"""
    
    # 服务器名称映射规则
    SERVER_NAME_MAPPING = {
        "diagnose": "cloudbot",  # 特殊映射
    }
    
    @staticmethod
    def get_server_configs() -> Dict[str, Dict[str, Any]]:
        """
        获取所有服务器配置
        
        Returns:
            Dict: {"servers": {server_name: config_dict}}
        """
        config = get_config()
        
        if not hasattr(config, 'mcp_servers'):
            return {"servers": {}}
        
        servers = {}
        mcp_servers = config.mcp_servers

        # 处理配置格式
        if isinstance(mcp_servers, dict):
            servers_dict = mcp_servers
        else:
            servers_dict = mcp_servers.__dict__

        # 转换每个服务器配置
        for server_name, server_config in servers_dict.items():
            if isinstance(server_config, dict):
                server_config = DotDict(server_config)
            
            # 标准化服务器名称
            normalized_name = MCPToolConfig._normalize_server_name(server_name)

            # 构建配置
            config_dict = MCPToolConfig._build_server_config(normalized_name, server_config)

            if config_dict:
                servers[normalized_name] = config_dict
        
        return {"servers": servers}
    
    @staticmethod
    def get_enabled_tools_map() -> Dict[str, List[str]]:
        """
        获取服务器到启用工具的映射
        
        Returns:
            Dict: {server_name: [tool_names]}
        """
        config = get_config()
        enabled_tools_map = {}
        mcp_servers = config.mcp_servers
        for server_name, server_config in mcp_servers.items():
            enabled_tools = server_config.get('enabled_tools', [])
            # 标准化服务器名称
            normalized_name = MCPToolConfig._normalize_server_name(server_name)
            enabled_tools_map[normalized_name] = enabled_tools or []
        return enabled_tools_map
    
    @staticmethod
    def _normalize_server_name(server_name: str) -> str:
        """标准化服务器名称"""
        return MCPToolConfig.SERVER_NAME_MAPPING.get(server_name, server_name)
    
    @staticmethod
    def _build_server_config(server_name: str, server_config: Any) -> Optional[Dict[str, Any]]:
        """构建服务器配置字典"""
        try:
            transport = getattr(server_config, 'protocol', 'streamable_http')
            timeout = getattr(server_config, 'timeout', 10)

            config_dict = {
                'name': server_name,
                'transport': transport,
                #'timeout': timeout, #StreamableHttp设置不是init 类型timeout，
                'enabled_tools': getattr(server_config, 'enabled_tools', []),
                'add_to_agents': getattr(server_config, 'add_to_agents', ["researcher"])
            }
            
            # 根据传输类型添加特定配置
            if transport == 'stdio':
                config_dict.update({
                    'command': getattr(server_config, 'command', 'npx'),
                    'args': getattr(server_config, 'args', [])
                })
            else:
                # HTTP传输
                url = MCPToolConfig._build_url(server_config)
                if url:
                    config_dict['url'] = url
                
                headers = MCPToolConfig._build_headers(server_config)
                if headers:
                    config_dict['headers'] = headers
            return config_dict
            
        except Exception:
            import traceback
            traceback.print_exc()
            return None
    
    @staticmethod
    def _build_url(server_config: Any) -> Optional[str]:
        """构建服务器URL"""
        base_url = getattr(server_config, 'base_url', '')
        if not base_url:
            return None
        
        path = getattr(server_config, 'path', '')
        url = f"{base_url.rstrip('/')}{path}"
        
        # Token认证
        auth_type = getattr(server_config, 'auth', '')
        token = getattr(server_config, 'token', '')
        
        if auth_type == 'token' and token:
            url = f"{url}?token={token}"
        
        return url
    
    @staticmethod
    def _build_headers(server_config: Any) -> Optional[Dict[str, str]]:
        """构建请求头"""
        auth_type = getattr(server_config, 'auth', '')
        token = getattr(server_config, 'token', '')
        
        if auth_type == 'bearer' and token:
            return {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
        
        return None

