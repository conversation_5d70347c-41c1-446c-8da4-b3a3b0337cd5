"""
MCPToolClient

职责：
- 管理MCP工具的Redis缓存
- 从MCP服务器获取工具
- 提供缓存刷新功能
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient

from deep_diagnose.common.utils.logging_utils import log_mcp_request
from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig

logger = logging.getLogger(__name__)


class MCPToolClient:
    """MCP工具缓存管理器"""
    def __init__(self):
        """初始化缓存管理器"""
        self.mcp_tool_config = MCPToolConfig()
        self.request_method = "list_tools"
        self.time_multiplier = 1000  # 毫秒转换

    async def get_all_tools(self) -> List[Tuple[str, List[BaseTool]]]:
        server_configs = self.mcp_tool_config.get_server_configs()
        servers = server_configs.get("servers", {})
        server_names = list(servers.keys())
        logger.info(f"Fetching tools from {len(server_names)} servers: {server_names}")
        try:
            # 并发获取所有服务器工具，传递服务器配置
            tasks = [self._fetch_server_tools_with_config(name, servers) for name in server_names]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            # 过滤掉异常并记录日志
            valid_results = []
            for result in results:
                if isinstance(result, BaseException):
                    import traceback
                    traceback.print_exc()
                    logger.error(f"Error fetching tools: {result}")
                else:
                    valid_results.append(result)
            return valid_results
        except BaseException as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Failed to fetch tools from servers: {e}")
            return []

    async def _fetch_server_tools_with_config(self, server_name: str, servers_config: Dict[str, Dict]) -> Tuple[str, List[BaseTool]]:
        """
        从单个服务器获取工具（使用提供的配置）
        
        Args:
            server_name: 服务器名称
            servers_config: 服务器配置字典
            
        Returns:
            Tuple: (server_name, tools)
        """
        start_time = int(datetime.now().timestamp() * self.time_multiplier)
        error_msg = None
        try:
            # 创建MCP客户端并获取工具
            client = MultiServerMCPClient(servers_config)
            tools = await client.get_tools(server_name=server_name)
            logger.info(f"Successfully fetched {len(tools)} tools from {server_name}")
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to fetch tools from {server_name}: {error_msg}")
            tools = []

        finally:
            end_time = int(datetime.now().timestamp() * self.time_multiplier)
            self._log_request(start_time, end_time, server_name, error_msg is None, error_msg)
        
        return server_name, tools

    def _log_request(self, start_time: int, end_time: int, server_name: str, 
                    success: bool, error: Optional[str] = None) -> None:
        """记录请求日志"""
        try:
            log_mcp_request(
                start_timestamp=start_time,
                end_timestamp=end_time,
                server_name=server_name,
                request_method=self.request_method,
                request_body={}
            )
            
            duration = end_time - start_time
            if success:
                logger.info(f"Fetched tools from {server_name} in {duration}ms")
            else:
                logger.warning(f"Failed to fetch from {server_name}: {error}")
                
        except Exception as e:
            logger.error(f"Failed to log request for {server_name}: {e}")


