"""
对百炼应用的调用封装
"""

import asyncio
import json
import logging
import time
from contextlib import aclosing
from enum import Enum
from http import HTTPStatus
from typing import AsyncGenerator, Iterator

from dashscope import Application

from deep_diagnose.common.config import get_config


class BailianAppEnum(Enum):
    """
    枚举类：百炼应用的app ID、工作流应用核心节点node ID（智能体应用则为None）
    """

    planner = ("50cc2154770042148b1e6f2af9586f6e", "LLM_Jw5I")
    param_extractor = ("e74bfaf654d74e18bf75eded4cd5405f", "LLM_OkGA")
    concluder = ("79484d517d6e4d66aaedc30c3589a676", "LLM_vsBT")
    concluder_no_think = ("1b7f6450301349478f0f8782701742f1", "LLM_vsBT")
    fast_concluder = ("e6e2983ac77047cc992aa04807185058", "Component_1hIy")
    question_classifier = ("086b040e5f96408c8aaa2473a31970a4", None)
    scenario_matcher = ("ddef12fc7f244c8888754d7ee77ef24b", None)

    overview_generator = ("2a52ff014ca344d9b69d037df4e865f8", None)
    recommendation_generator = ("f3f6eedfbb334235ad999149d06d6f79", None)
    question_rewriter = ("0f24b70173164daca34116d6ab44a85b", None)


class AppBailian:
    def __init__(self, api_key: str):
        """
        初始化AppBailian实例，设置百炼应用调用所需的API密钥。

        参数:
            api_key (str): 用于认证百炼应用调用的API密钥。
        """
        self.__api_key__ = api_key

    async def app_call(self, app: BailianAppEnum, prompt: str, biz_params=None) -> str | None:
        """
        异步调用百炼应用并聚合流式响应结果为完整文本。

        参数:
            app (BailianAppEnum): 指定调用的百炼应用实例，使用枚举类型标识不同的应用。
            prompt (str): 输入给模型的提示文本。
            biz_params (dict): 业务参数，用于传递额外的上下文信息。

        返回值:
            str | None: 聚合后的完整响应文本，若调用失败或无结果则返回None。
        """
        # 调用流式接口，获取异步生成器
        start_ts = time.time()
        responses = self.app_stream_call(app, prompt, biz_params)
        contents = []
        try:
            async with aclosing(responses) as agen:  # 自动安全关闭
                async for result, _ in agen:
                    if result is not None:
                        contents.append(result)
        except Exception as e:
            logger.error(f"Error during bailian app call: {e}", exc_info=True)
            contents = []

        end_ts = time.time()
        logger.info(f"Call of {app.name} costs {end_ts - start_ts:.3f} s")
        return None if len(contents) == 0 else str.join("", contents)

    def app_stream_call(self, app: BailianAppEnum, prompt: str, biz_params=None) -> AsyncGenerator:
        """
        调用百炼应用的流式接口，返回异步生成器以逐步获取处理结果。

        参数:
            app (BailianAppEnum): 指定调用的百炼应用实例，包含app ID和目标节点ID。
            prompt (str): 输入给模型的提示文本。
            biz_params (dict): 业务参数，用于传递额外的上下文信息。

        返回值:
            AsyncGenerator: 异步生成器，用于逐步产生处理结果。
        """

        # 调用百炼应用的流式接口，启用流式输出
        responses = Application.call(
            api_key=self.__api_key__,
            app_id=app.value[0],
            prompt=prompt,
            biz_params=biz_params if biz_params else {},
            # 开启流式输出
            stream=True,
            incremental_output=True,
            # 输出思考过程
            has_thoughts=True,
        )
        # 将同步迭代器包装为异步生成器返回
        return _make_async_iterator(iter(responses), node_id=app.value[1])


def _load_key_from_env() -> str:
    """
    从环境配置中加载百炼应用所需的API密钥。

    返回值:
        str: 用于认证百炼应用调用的API密钥。
    """
    key = get_config().llm.tongyi_provider.api_key
    if not isinstance(key, str):
        raise ValueError("API密钥配置错误：期望字符串类型")
    return key


async def _make_async_iterator(sync_iterator: Iterator, node_id: str | None) -> AsyncGenerator:
    """
    将同步迭代器包装为异步生成器，用于逐步提取并过滤百炼应用流式响应中指定节点的结果。

    参数:
        sync_iterator (Iterator): 同步迭代器对象，提供百炼应用的原始响应流。
        node_id (str): 需要筛选的目标节点ID，用于提取特定节点的处理结果。

    返回值:
        AsyncGenerator: 异步生成器，逐步产生元组 (result, reason)，其中：
            result (str | None): 节点处理结果，若不存在则为None。
            reason (str | None): 节点的推理过程内容，若不存在则为None。
    """

    while True:
        try:
            # 使用线程池执行同步迭代操作，避免阻塞事件循环。同时增加超时，避免阻塞太久
            item = await asyncio.wait_for(
                asyncio.to_thread(_safe_next, sync_iterator),
                timeout=10.0
            )
            # 响应状态码非HTTPStatus.OK，记录错误日志并返回
            if item.status_code != HTTPStatus.OK:
                logger.error(
                    f"Error during bailian app call. request_id={item.request_id}, code={item.status_code}, message={item.message}",
                    exc_info=True)
                break
            # 跳过空响应
            if item.output.thoughts is None and item.output.text is None:
                continue
            # 从响应中提取思考和回复
            if node_id is None or node_id == "":
                # 智能体应用
                reason = item.output.thoughts[0].thought if item.output.thoughts is not None else None
                result = item.output.text
                yield result, reason
            else:
                # 工作流应用，筛选出指定节点的响应
                for thought in item.output.thoughts:
                    node_info = json.loads(thought.response)
                    if "nodeId" in node_info and node_info["nodeId"] == node_id and "nodeResult" in node_info:
                        node_result = json.loads(node_info["nodeResult"])
                        result = node_result.get("result", None)
                        reason = node_result.get("reasoningContent", None)
                        yield result, reason
        except StopIteration:
            # 同步迭代器正常结束
            break
        except Exception as e:
            if e.args and "StopIteration" not in e.args[0]:
                # 处理特殊情况下的StopIteration异常，视为正常结束。记录其他错误信息
                logger.error(f"Error during async iteration: {e}", exc_info=True)
            break


def _safe_next(iterator):
    """
    安全地从迭代器中获取下一个元素。

    该函数对内置的 next() 函数进行了封装，当迭代器耗尽时，
    将 StopIteration 异常转换为 RuntimeError 异常抛出，
    以避免在某些异步上下文中可能出现的问题。

    参数:
        iterator (Iterator): 输入的同步迭代器对象。

    返回值:
        任意类型: 迭代器中的下一个元素。

    异常:
        RuntimeError: 当迭代器耗尽时抛出，携带 "StopIteration Safe Package" 信息。
    """
    try:
        return next(iterator)
    except StopIteration:
        raise RuntimeError("StopIteration Safe Package")


# 模块初始化代码
logger = logging.getLogger(__name__)
app_bailian = AppBailian(_load_key_from_env())

if __name__ == "__main__":
    res = app_bailian.app_call(BailianAppEnum.recommendation_generator, "请根据以下信息生成推荐内容：\n\n")
    stream_res = app_bailian.app_stream_call(BailianAppEnum.overview_generator, "请根据以下信息生成内容总结：\n\n")

    result = asyncio.run(res)
    print(result)


    async def stream_output(stream_res):
        async for result, _ in stream_res:
            print(result, end="")


    asyncio.run(stream_output(stream_res))
