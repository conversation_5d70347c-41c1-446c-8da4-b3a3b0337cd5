"""
Chat Event Envelope utilities

Responsibilities:
- EventEnvelope: wrap BaseAgentOutputEvent with a seq for ordered delivery
- parse_envelope: accept both new/legacy payloads and return (seq, event_json_str)

External interfaces:
- EventEnvelope(seq: Optional[int], event: BaseAgentOutputEvent).to_json_str() -> str
- parse_envelope(payload: str) -> tuple[Optional[int], Optional[str]]
"""
import json
from dataclasses import dataclass
from typing import Optional, Tuple

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent


@dataclass
class EventEnvelope:
    """统一的事件信封格式。
    - 内部字段 event 保存事件对象对应的 dict（非字符串），避免双层字符串。
    - 兼容旧格式：{"data": "<event_json>"}
    """
    seq: Optional[int]
    event: BaseAgentOutputEvent

    def to_json_str(self) -> str:
        payload = {
            "seq": self.seq,
            "event": json.loads(self.event.to_sse_format()),
        }
        return json.dumps(payload, ensure_ascii=False)


def parse_envelope(payload: str) -> <PERSON>ple[Optional[int], Optional[str]]:
    """解析事件信封，输出 (seq, event_json_str)
    - 支持新格式: {"seq": n, "event": {...}}
    - 兼容旧格式: {"seq": n, "data": "<json>"} 或 {"data": "<json>"}
    - 若 payload 本身就是事件 JSON，则直接返回
    """
    try:
        data = json.loads(payload)
        seq = data.get("seq") if isinstance(data, dict) else None

        # 新格式: event 为 dict 或 json 字符串
        if isinstance(data, dict) and "event" in data:
            ev = data["event"]
            if isinstance(ev, dict):
                return (int(seq) if seq is not None else None, json.dumps(ev, ensure_ascii=False))
            if isinstance(ev, str):
                return (int(seq) if seq is not None else None, ev)

        # 旧格式: data 字段
        if isinstance(data, dict) and "data" in data:
            ev = data["data"]
            if isinstance(ev, dict):
                return (int(seq) if seq is not None else None, json.dumps(ev, ensure_ascii=False))
            if isinstance(ev, str):
                return (int(seq) if seq is not None else None, ev)

        # 如果是一个 dict，但没有 event/data 字段，可能就是事件对象本身
        if isinstance(data, dict):
            return (int(seq) if seq is not None else None, json.dumps(data, ensure_ascii=False))

        # 其他情况直接返回原始字符串
        return (int(seq) if seq is not None else None, payload)
    except Exception:
        # 非 JSON 字符串，直接返回，让上层容错
        return (None, payload)
