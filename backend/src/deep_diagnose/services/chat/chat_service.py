"""
Chat Service V1 - 重构后的主服务文件

职责：
1. 数据模型定义（ChatContext, SessionInfo）
2. 主服务类（ChatServiceV1）
3. 工厂函数

管理器组件已拆分到 managers/ package
请求处理器已拆分到 request_processors.py
"""

import logging
from typing import List, Dict, Any, AsyncGenerator, Optional

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import CloudbotAgentChatSession, MessageType, MessageStatus
from deep_diagnose.domain.chat.repository import chat_repository
from deep_diagnose.services.chat.managers.session_manager import ChatContext
from deep_diagnose.storage.redis_client import RedisClient
from .events.chat_event_envelope import parse_envelope
from .request_processors import SynchronousRequestProcessor, AsynchronousRequestProcessor

logger = logging.getLogger(__name__)

# 配置常量
LONG_RUNNING_AGENTS = {"ReasoningAgent"}
INTERACTIVE_AGENTS = {"InspectAgent", "InteractiveAgent"}


def is_async_agent(agent_name: str) -> bool:
    """判断是否为长时间运行的Agent"""
    return agent_name in LONG_RUNNING_AGENTS


class ChatService:
    """
    V1版本的聊天服务 - 重构后的主服务

    职责：
    1. 协调请求处理器
    2. 提供统一的聊天接口
    3. 向后兼容的辅助方法
    """

    def __init__(self):
        """初始化聊天服务V1"""
        self.current_session_id: Optional[str] = None
        self.redis_client = RedisClient()

        # 初始化请求处理器
        self.sync_processor = SynchronousRequestProcessor(self.redis_client)
        self.async_processor = AsynchronousRequestProcessor(self.redis_client)

    async def chat(
        self,
        question: str,
        user_id: str,
        agent: str = "ReasoningAgent",
        session_id: Optional[str] = None,
        messages: Optional[List[Dict[str, Any]]] = None,
        request_id: Optional[str] = None,
        **kwargs,
    ) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """
        V1聊天服务的核心方法 - 支持长时间运行请求
        Args:
            question: 用户问题
            user_id: 用户ID
            agent: 智能体类型
            session_id: 会话ID
            messages: 历史消息列表
            request_id: 请求ID（用于长时间运行任务）
            **kwargs: 其他参数

        Yields:
            BaseAgentOutputEvent: 结构化事件对象
        """
        # 创建聊天上下文
        context = ChatContext(
            question=question,
            user_id=user_id,
            agent=agent,
            session_id=session_id,
            messages=messages,
            request_id=request_id,
            kwargs=kwargs,
        )

        # 选择合适的处理器
        if is_async_agent(agent):
            processor = self.async_processor
        else:
            processor = self.sync_processor

        # 处理请求并设置session_id
        session_set = False
        async for event in processor.process(context):
            # 在第一个事件时设置session_id
            if not session_set and hasattr(processor, "_current_session_id"):
                self.current_session_id = processor._current_session_id
                session_set = True
            yield event

    async def get_user_sessions(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取用户的聊天会话列表并格式化为API响应格式

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            格式化的会话列表
        """
        try:
            # 获取用户的会话列表，按创建时间倒序
            sessions = await chat_repository.get_user_sessions(user_id=user_id, limit=limit)

            # 转换为响应格式
            session_responses = [
                {"session_id": session.session_id, "title": session.subject or "未命名会话", "gmt_create": session.gmt_create} for session in sessions
            ]

            logger.info(f"Formatted {len(session_responses)} sessions for user {user_id}")
            return session_responses

        except Exception as e:
            logger.error(f"Failed to get formatted sessions for {user_id}: {e}")
            raise

    async def get_session_messages(self, session_id: str) -> List[Dict[str, Any]]:
        """
        获取会话的消息列表并格式化为API响应格式，包含Redis实时数据查询

        Args:
            session_id: 会话ID

        Returns:
            格式化的消息列表
        """
        try:
            # 检查会话是否存在
            session = await chat_repository.get_session_by_id(session_id)
            if not session:
                from fastapi import HTTPException

                raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")

            # 获取会话的消息列表，按创建时间正序
            messages = await chat_repository.get_session_messages(session_id=session_id)

            message_responses = []
            for message in messages:
                message_content = message.message

                # 如果消息未完成(status=executing)且是AI消息，尝试从Redis获取最新内容
                if message.status == MessageStatus.EXECUTING.value and message.message_type == MessageType.AI_RESPONSE.value and message.request_id:
                    try:
                        # 通过事件总线读取快照，屏蔽底层缓存细节
                        from .chat_event_bus import ChatEventBus
                        event_bus = ChatEventBus(self.redis_client)
                        redis_data = await event_bus.read_snapshot(message.request_id)
                        message_content = ''
                        if redis_data:
                            # 解析Redis中的事件数据（兼容新旧两种信封格式）
                            _, event_json = parse_envelope(str(redis_data))
                            message_content = event_json or str(redis_data)

                    except Exception as e:
                        logger.warning(f"Failed to get Redis data for message {message.id}: {e}")

                message_responses.append(
                    {"message": message_content, "agent": message.agent, "message_type": message.message_type, "gmt_create": message.gmt_create}
                )

            logger.info(f"Formatted {len(message_responses)} messages for session {session_id}")
            return message_responses

        except Exception as e:
            logger.error(f"Failed to get formatted messages for session {session_id}: {e}")
            raise

    async def get_session_by_id(self, session_id: str) -> Optional[CloudbotAgentChatSession]:
        """
        根据session_id获取会话信息

        Args:
            session_id: 会话ID

        Returns:
            会话对象，如果不存在则返回None
        """
        try:
            return await chat_repository.get_session_by_id(session_id)
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None


def create_chat_service() -> ChatService:
    """创建聊天服务V1实例"""
    return ChatService()
