"""
Chat Session Manager

处理会话创建和消息管理
"""

import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from uuid import uuid4

from deep_diagnose.domain.chat.models import MessageStatus, MessageType
from deep_diagnose.domain.chat.repository import chat_repository

logger = logging.getLogger(__name__)


@dataclass
class SessionInfo:
    """会话信息数据类"""

    session_id: str
    message_id: int
    messages: Optional[List[Dict[str, Any]]] = None


@dataclass
class ChatContext:
    """聊天上下文数据类"""

    question: str
    user_id: str
    agent: str
    session_id: Optional[str] = None
    messages: Optional[List[Dict[str, Any]]] = None
    request_id: Optional[str] = None
    kwargs: Dict[str, Any] = None  # type: ignore

    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if not self.request_id:
            self.request_id = f"req_{uuid4()}"


class SessionManager:
    """会话管理器 - 处理会话创建和消息管理"""

    @staticmethod
    async def setup_session(context: ChatContext) -> "SessionInfo":
        """设置会话信息"""
        session_id = context.session_id
        request_id = context.request_id
        if not request_id:
            request_id = f"req_{uuid4()}"
        # 1. 检查会话ID
        check_session_exist = await SessionManager._check_session_exist(context)
        if not session_id or len(session_id.strip()) == 0 or not check_session_exist:
            # 创建新会话
            session_info = await SessionManager._create_new_session(context)
        else:
            # 加载历史会话
            session_info = await SessionManager._load_existing_session(context)

        # 创建用户消息记录
        message_id = await SessionManager._create_user_message(
            session_id=session_info.session_id,
            request_id=request_id,
            question=context.question,
            agent=context.agent,
            additional_info=context.kwargs.get("additional_info", {}),
        )
        session_info.message_id = message_id

        return session_info

    @staticmethod
    async def _check_session_exist(context: ChatContext) -> bool:
        """检查会话ID是否存在"""
        if not context.session_id:
            return False
        session = await chat_repository.get_session_by_id(context.session_id)
        return session is not None

    @staticmethod
    async def _create_new_session(context: ChatContext) -> "SessionInfo":
        """创建新会话"""

        subject = context.question[:50] + "..." if len(context.question) > 50 else context.question
        chat_session = await chat_repository.create_session(session_id=context.session_id, user_id=context.user_id, subject=subject)
        logger.info(f"Created new session {chat_session.session_id} for user {context.user_id}")
        return SessionInfo(session_id=chat_session.session_id, message_id=0, messages=None)

    @staticmethod
    async def _load_existing_session(context: ChatContext) -> "SessionInfo":
        """加载现有会话"""

        messages = []
        if context.session_id is None:
            history_messages = []
        else:
            history_messages = await chat_repository.get_session_messages(context.session_id)
        for msg in history_messages:
            role = "user" if msg.message_type == "human_query" else "assistant"
            messages.append({"role": role, "content": msg.message})
        logger.info(f"Loaded {len(messages)} history messages for session {context.session_id}")
        return SessionInfo(session_id=context.session_id, message_id=0, messages=messages)

    @staticmethod
    async def _create_user_message(session_id: str, request_id: str, question: str, agent: str, additional_info: dict = {}) -> int:
        """创建用户消息记录"""
        user_message = await chat_repository.create_message(
            session_id=session_id,
            message=question,
            message_type="human_query",
            request_id=request_id,
            agent=agent,
            status=MessageStatus.SUCCESS.value,
            ext=additional_info,
        )
        logger.info(f"Created user message {user_message.id}")
        return user_message.id

    @staticmethod
    async def create_ai_message_placeholder(session_id: str, request_id: str, agent: str) -> int:
        """创建AI消息占位符（状态为未完成）"""
        try:
            ai_message = await chat_repository.create_message(
                session_id=session_id,
                message="",
                message_type=MessageType.AI_RESPONSE.value,
                request_id=request_id,
                agent=agent,
                status=MessageStatus.EXECUTING.value,
            )
            logger.info(f"Created AI message placeholder {ai_message.id} for session {session_id}")
            return ai_message.id
        except Exception as e:
            logger.error(f"Failed to create AI message placeholder: {e}")
            raise

    @staticmethod
    async def persist_ai_response(session_id: str, request_id: str, result_content: Dict[str, Any], agent: str) -> None:
        """持久化AI回复"""
        try:
            ai_message = await chat_repository.create_message(
                session_id=session_id,
                message=result_content.get("message", ""),
                message_type=MessageType.AI_RESPONSE.value,
                request_id=request_id,
                agent=agent,
                status=result_content.get("status", MessageStatus.SUCCESS.value),
                ext=result_content.get("ext", {}),
            )
            logger.info(f"Created AI message {ai_message.id} for session {session_id}")
        except Exception as e:
            logger.error(f"Failed to persist AI response: {e}")

    @staticmethod
    async def update_ai_message_content(ai_message_id: int, result_content: str) -> None:
        """更新AI消息内容并标记为完成"""
        try:
            await chat_repository.update_message(ai_message_id, message=result_content, status=MessageStatus.SUCCESS.value)  # 标记为成功完成
            logger.info(f"Updated AI message {ai_message_id} and marked as completed")
        except Exception as e:
            logger.error(f"Failed to update AI message content: {e}")
