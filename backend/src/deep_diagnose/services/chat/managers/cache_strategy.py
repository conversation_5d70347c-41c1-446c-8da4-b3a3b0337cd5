"""
Chat Cache Strategy

Redis缓存策略实现
"""

import asyncio
import logging
import time

from deep_diagnose.storage.redis_client import RedisClient

logger = logging.getLogger(__name__)

# 配置常量
DEFAULT_CACHE_TTL = 24 * 3600  # 24小时


class CacheStrategy:
    """Redis缓存策略"""

    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self.last_cache_time = 0
        self.cache_interval = 2.0  # 最小缓存间隔2秒
        self.pending_data = None
        self.cache_task = None

    def should_cache_now(self) -> bool:
        """判断是否应该立即缓存"""
        current_time = time.time()
        return (current_time - self.last_cache_time) >= self.cache_interval

    async def cache_with_strategy(self, key: str, data: str, ttl_seconds: int = DEFAULT_CACHE_TTL):
        """使用策略进行缓存"""
        self.pending_data = (key, data, ttl_seconds)

        # 如果达到缓存间隔，立即缓存
        if self.should_cache_now():
            await self._execute_cache()
        else:
            # 否则延迟缓存，确保最后的数据不丢失
            self._schedule_delayed_cache()

    async def _execute_cache(self):
        """执行缓存操作"""
        if self.pending_data:
            key, data, ttl_seconds = self.pending_data
            try:
                self.redis_client.set_cache(key, data, ttl_seconds)
                self.last_cache_time = time.time()
                logger.debug(f"Cached data for key: {key}")
            except Exception as e:
                logger.error(f"Failed to cache data: {e}")
            finally:
                self.pending_data = None

    def _schedule_delayed_cache(self):
        """安排延迟缓存"""
        if self.cache_task and not self.cache_task.done():
            self.cache_task.cancel()

        # 延迟缓存，确保最后的数据被保存
        delay = self.cache_interval - (time.time() - self.last_cache_time)
        self.cache_task = asyncio.create_task(self._delayed_cache(max(delay, 0.5)))

    async def _delayed_cache(self, delay: float):
        """延迟缓存执行"""
        await asyncio.sleep(delay)
        await self._execute_cache()

    async def force_cache(self):
        """强制缓存（用于确保最后数据不丢失）"""
        if self.cache_task and not self.cache_task.done():
            self.cache_task.cancel()
        await self._execute_cache()