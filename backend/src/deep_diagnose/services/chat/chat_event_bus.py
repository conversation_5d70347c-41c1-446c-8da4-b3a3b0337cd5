"""
Chat Event Bus: unified Redis Pub/Sub + snapshot cache operations

- Publishes events to channel with a consistent envelope
- Writes latest snapshot to key for replay/fallback
- Subscribes to channel with an async iterator interface

Channel/key format:
- Channel: CHAT_EVENT:channel:{request_id}
- Key:     CHAT_EVENT:{request_id}
"""
from __future__ import annotations

from typing import Optional

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import RedisKeyPrefix
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.storage.redis_pubsub_stream import RedisPubSubStream
from .events.chat_event_envelope import EventEnvelope


class ChatEventBus:
    """Chat domain specific event bus built on Redis.

    Responsibilities:
    - Provide stable channel/key naming for a given request_id
    - Publish structured events (EventEnvelope) to Pub/Sub channels
    - Maintain a latest snapshot per request for replay/fallback
    - Expose a subscribe() that returns a unified async stream
    - Encapsulate snapshot TTL and read/write operations
    """

    SNAPSHOT_TTL_SECONDS = 6 * 3600  # 6 hours

    def __init__(self, redis_client: RedisClient) -> None:
        self._redis = redis_client

    @staticmethod
    def channel_name(request_id: str) -> str:
        return f"{RedisKeyPrefix.CHAT_EVENT}:channel:{request_id}"

    @staticmethod
    def key_name(request_id: str) -> str:
        return f"{RedisKeyPrefix.CHAT_EVENT}:{request_id}"

    async def publish(self, request_id: str, seq: int, event: BaseAgentOutputEvent) -> None:
        channel = self.channel_name(request_id)
        payload = EventEnvelope(seq=seq, event=event).to_json_str()
        await self._redis.publish_async(channel, payload)

    async def write_snapshot(self, request_id: str, seq: int, event: BaseAgentOutputEvent) -> None:
        key = self.key_name(request_id)
        payload = EventEnvelope(seq=seq, event=event).to_json_str()
        await self._redis.set_cache_async(key, payload, ttl_seconds=self.SNAPSHOT_TTL_SECONDS)

    async def read_snapshot(self, request_id: str) -> Optional[str]:
        """Read the latest snapshot for a request_id as a JSON string (EventEnvelope).
        Returns None when not found.
        """
        key = self.key_name(request_id)
        try:
            return await self._redis.get_cache_async(key)
        except Exception:
            # Fallback to sync get to maximize compatibility
            try:
                return self._redis.get_cache(key)
            except Exception:
                return None

    async def subscribe(self, request_id: str) -> RedisPubSubStream:
        channel = self.channel_name(request_id)
        stream = RedisPubSubStream(self._redis)
        await stream.subscribe(channel)
        return stream
