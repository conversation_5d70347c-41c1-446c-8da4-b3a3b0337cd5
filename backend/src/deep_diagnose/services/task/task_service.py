"""
任务管理服务模块

提供异步任务的创建、存储、状态管理和执行功能。
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Optional
from uuid import uuid4

from deep_diagnose.common.config.core.environment import get_environment, Environment
from deep_diagnose.common.models.task_models import TaskStatus, TaskStatusResponse, TaskData
from deep_diagnose.services.task.celery_base import celery
from deep_diagnose.services.task.celery_tasks import execute_diagnosis_task, cancel_diagnosis_task
from deep_diagnose.services.task.task_executor import TaskExecutor
from deep_diagnose.storage.redis_client import RedisClient

logger = logging.getLogger(__name__)


class TaskService:
    """任务管理服务"""

    def __init__(self):
        self.redis_client = RedisClient()
        self.environment = get_environment()
        self.use_celery = self._should_use_celery()
        
        if not self.use_celery:
            logger.info(f"Running in {self.environment.value} environment - tasks will execute directly")
        else:
            logger.info(f"Running in {self.environment.value} environment - tasks will use Celery")
    
    def _should_use_celery(self) -> bool:
        """
        判断是否应该使用 Celery
        
        Returns:
            bool: True 表示使用 Celery，False 表示直接执行
        """
        # 在开发环境中，可以通过环境变量控制是否使用 Celery
        force_celery = os.getenv('FORCE_CELERY', '').lower() in ('true', '1', 'yes')
        
        if force_celery:
            return True
            
        # 开发环境默认不使用 Celery，其他环境使用 Celery
        return self.environment != Environment.DEVELOPMENT

    async def create_task(self, agent: str, question: str, user_id: str = None) -> str:
        """
        创建新任务

        Args:
            agent: 智能体名称
            question: 问题描述
            user_id: 用户ID（可选）

        Returns:
            任务ID
        """
        task_id = str(uuid4())
        task_data = {
            "task_id": task_id,
            "agent": agent,
            "question": question,
            "user_id": user_id,
            "status": TaskStatus.PENDING.value,
            "submitted_at": datetime.utcnow().isoformat() + "Z",
            "completed_at": None,
            "data": {
                "result": None,
                "detail": None,
                "error": None
            }
        }

        # 存储到Redis，设置24小时过期时间
        task_key = f"cloudbot_agent:{task_id}"
        self.redis_client.set_cache(task_key, json.dumps(task_data), ttl_seconds=24 * 3600)

        if self.use_celery:
            # 通过 Celery 异步启动任务执行
            celery_task = execute_diagnosis_task.delay(task_id, agent.strip(), question.strip())
            logger.info(f"Created task {task_id} for agent {agent}, Celery task ID: {celery_task.id}")
            
            # 将 Celery 任务 ID 存储到 Redis 中，用于后续跟踪
            task_data["celery_task_id"] = celery_task.id
            self.redis_client.set_cache(task_key, json.dumps(task_data), ttl_seconds=24 * 3600)
        else:
            # 开发环境：直接执行任务
            logger.info(f"Created task {task_id} for agent {agent} - executing directly")
            asyncio.create_task(self._execute_task_directly(task_id, agent.strip(), question.strip()))
        
        return task_id

    async def _execute_task_directly(self, task_id: str, agent: str, question: str):
        """
        直接执行任务（开发环境使用）
        
        Args:
            task_id: 任务ID
            agent: 智能体名称
            question: 问题描述
        """
        try:
            # 创建任务执行器实例
            executor = TaskExecutor()
            
            # 执行任务
            await executor.execute_task(task_id, agent, question)
            logger.info(f"Direct task execution completed for {task_id}")
            
        except Exception as e:
            logger.error(f"Direct task execution failed for {task_id}: {e}")
            # 更新任务状态为失败
            try:
                executor = TaskExecutor()  # 只用于更新状态
                await executor._update_task(task_id, status=TaskStatus.FAILURE, error=str(e))
            except Exception as update_error:
                logger.error(f"Failed to update task status for {task_id}: {update_error}")

    async def get_task(self, task_id: str) -> Optional[TaskStatusResponse]:
        """
        获取任务信息
        Args:
            task_id: 任务ID
        Returns:
            任务状态响应对象，如果任务不存在则返回None
        """
        task_key = f"cloudbot_agent:{task_id}"
        task_data_str = self.redis_client.get_cache(task_key)
        if not task_data_str:
            return None
        try:
            task_data = json.loads(task_data_str)
            logger.info(f"get_task for {task_id}: Retrieved task_data with status {task_data['status']};"
                        f"data.result length: {len(task_data['data']['result']) if task_data['data']['result'] else 0};"
                        f"data.detail length: {len(task_data['data']['detail']) if task_data['data']['detail'] else 0};"
                        f"data.error: {task_data['data']['error']}")

            should_create_data = task_data["data"]["result"] or task_data["data"]["error"] or task_data["data"]["detail"]
            return TaskStatusResponse(
                task_id=task_data["task_id"],
                status=TaskStatus(task_data["status"]),
                submitted_at=task_data["submitted_at"],
                completed_at=task_data.get("completed_at"),
                data=TaskData(**task_data["data"]) if should_create_data else None
            )
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Failed to parse task data for {task_id}: {e}")
            return None

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        Args:
            task_id: 任务ID
        Returns:
            bool: 是否成功取消任务
        """
        try:
            if self.use_celery:
                # Celery 模式：取消 Celery 任务
                task_key = f"cloudbot_agent:{task_id}"
                task_data_str = self.redis_client.get_cache(task_key)
                
                if task_data_str:
                    task_data = json.loads(task_data_str)
                    celery_task_id = task_data.get("celery_task_id")
                    
                    if celery_task_id:
                        # 取消 Celery 任务
                        celery.control.revoke(celery_task_id, terminate=True)
                        logger.info(f"Revoked Celery task {celery_task_id} for task {task_id}")
                
                # 通过 Celery 任务更新任务状态
                cancel_diagnosis_task.delay(task_id)
                logger.info(f"Task {task_id} cancellation initiated via Celery")
            else:
                # 直接执行模式：直接更新任务状态为取消
                executor = TaskExecutor()  # 只用于更新状态
                await executor._update_task(task_id, status=TaskStatus.CANCEL)
                logger.info(f"Task {task_id} cancelled directly")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False


# 全局任务服务实例
task_service = TaskService()
