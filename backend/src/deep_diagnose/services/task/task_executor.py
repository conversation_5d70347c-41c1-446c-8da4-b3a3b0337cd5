"""
任务执行器模块

负责异步执行诊断任务，通过ChatService执行工作流并拼接SSE内容。
"""

import json
import logging
import time
import traceback
from datetime import datetime
from typing import List, Dict, Any

from deep_diagnose.common.models.task_models import TaskStatus
from deep_diagnose.core.reasoning.report_generator import create_html_generator, create_auto_generator
from deep_diagnose.services.chat.chat_service_deprecated import ChatServiceDeprecated
from deep_diagnose.services.sse.sse_message_parser import SSEMessageParser
from deep_diagnose.storage.redis_client import RedisClient

logger = logging.getLogger(__name__)


class TaskExecutor:
    """任务执行器"""

    def __init__(self):
        self.redis_client = RedisClient()
        # 直接创建 ChatService 实例，避免全局状态
        self.chat_service = ChatServiceDeprecated()

    async def execute_task(self, task_id: str, agent: str, question: str):
        """
        异步执行诊断任务，通过ChatService执行工作流并生成PDF报告

        Args:
            task_id: 任务ID
            agent: 智能体名称
            question: 问题描述

        Returns:
            tuple: (complete_sse_data, report_markdown)
        """
        try:
            # 更新任务状态为处理中
            await self._update_task(task_id, status=TaskStatus.PROCESSING)
            logger.info(f"Starting task execution for {task_id}")
            # 构建消息
            messages = [{"role": "user", "content": question}]

            # 执行工作流并收集完整SSE数据
            final_detail = await self._run_agent(messages, task_id)
            report_markdown = final_detail.get("final_answer", "")
            # 生成HTML报告
            report_path = await self._generate_report(task_id, report_markdown)
            logger.info(f"Task {task_id}: Report generated at: {report_path}")
            # 将report路径添加到报告中
            if report_path:
                final_detail["urls"].append({
                    "name": "CloudBot智能体-长推理诊断报告",
                    "url": report_path
                })

            await self._update_task(task_id, status=TaskStatus.SUCCESS, result=report_markdown,
                                    detail=json.dumps(final_detail, ensure_ascii=False))
            logger.info(f"Task {task_id} completed successfully with structured detail")

            # 返回两种内容
            return json.dumps(final_detail, ensure_ascii=False), report_markdown

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            logger.error(f"Task {task_id} failed: {e}\n{traceback.format_exc()}")

            # 标记任务失败
            await self._update_task(task_id, status=TaskStatus.FAILURE, error=error_msg)

            # 返回错误信息
            return error_msg, error_msg

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消正在执行的任务
        Args:
            task_id: 任务ID
        Returns:
            task_status
        """
        try:
            # 更新任务状态为取消
            await self._update_task(task_id, status=TaskStatus.CANCEL)
            logger.info(f"Task {task_id} canceled")
            return True
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False

    async def _run_agent(self, messages: List[Dict[str, Any]], task_id: str) -> Dict[str, Any]:
        """
        运行工作流并收集完整SSE数据，返回格式化的完整数据和报告

        Args:
            messages: 消息列表
            task_id: 任务ID

        Returns:
            tuple: (complete_sse_data, report_markdown, structured_data)
        """
        try:
            # 使用专门的SSE消息解析器
            sse_parser = SSEMessageParser()
            last_redis_update_time = 0
            redis_update_interval = 10  # 秒
            plan_sent_to_redis = False

            logger.info(f"Task {task_id}: Starting workflow execution with SSE parser")

            # 从messages中提取问题
            question = ""
            if messages:
                # 找到最后一个用户消息作为问题
                for msg in reversed(messages):
                    if msg.get("role") == "user":
                        question = msg.get("content", "")
                        break
            
            # 调用 ChatService 的聊天方法
            async for sse_event in self.chat_service.chat(
                    question=question,
                    user_id="-1",
                    session_id=None,
                    messages=messages
            ):
                # 使用SSE解析器处理事件
                content_added = sse_parser.process_sse_event(sse_event, task_id)

                # 智能Redis更新策略
                if content_added:
                    current_time = time.time()
                    time_since_last = current_time - last_redis_update_time

                    collected_data = sse_parser.get_collected_data()
                    plan_is_ready = collected_data["planner_plan"] is not None
                    is_first_plan_update = plan_is_ready and not plan_sent_to_redis
                    is_time_for_update = time_since_last >= redis_update_interval

                    if is_first_plan_update or is_time_for_update:
                        # 构建当前进度数据
                        current_report = sse_parser.build_final_report()
                        current_detail = sse_parser.build_structured_detail()  # 中间更新不包含HTML路径

                        # 更新Redis
                        await self._update_task(
                            task_id,
                            status=TaskStatus.PROCESSING,
                            result=current_report,
                            detail=json.dumps(current_detail, ensure_ascii=False)
                        )
                        last_redis_update_time = current_time
                        logger.debug(
                            f"Task {task_id}: Redis updated - First plan: {is_first_plan_update}, Timed: {is_time_for_update}")
                        if is_first_plan_update:
                            plan_sent_to_redis = True

            # 最终更新Redis，确保所有数据都被保存
            final_report = sse_parser.build_final_report()
            final_detail = sse_parser.build_structured_detail()

            # 最终更新Redis（这里还没有HTML路径）
            await self._update_task(
                task_id,
                status=TaskStatus.PROCESSING,
                result=final_report,
                detail=json.dumps(final_detail, ensure_ascii=False)
            )

            return final_detail

        except Exception as e:
            logger.error(f"Workflow execution with complete SSE failed for task {task_id}: {e}")
            raise

    async def _generate_report(self, task_id: str, report: str, report_type="html") -> str | None:
        """
        生成HTML报告
        Args:
            task_id: 任务ID
            report: 诊断报告
        Returns:
            HTML文件路径或OSS URL，如果生成失败则返回None
        """
        try:
            if report_type == "html":
                # 生成HTML（启用OSS上传）
                html_generator = create_html_generator(enable_oss=True)
                result = html_generator.generate_simple_report(task_id=task_id, content=report)
            elif report_type == "pdf":
                pdf_generator = create_auto_generator(enable_oss=True)
                result = pdf_generator.generate_simple_report(task_id=task_id, content=report)
            if result.success:
                # 优先返回OSS URL，如果没有则返回本地路径
                final_path = result.oss_url or result.file_path
                logger.info(f"Generated report for task {task_id}: {final_path}")
                if result.oss_url:
                    logger.info(f"Report uploaded to OSS: {result.oss_url}")
                return final_path
            else:
                logger.error(f"Failed to generate report for task {task_id}: {result.error_message}")
                return None
        except Exception as e:
            logger.error(f"Failed to generate report for task {task_id}: {e}")
            return None

    async def _update_task(self, task_id: str, status: TaskStatus, result: str = None, detail: str = None,
                           error: str = None):
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            result: 结果（可选）
            detail: 详细信息（可选）
            error: 错误信息（可选）
        """
        task_key = f"cloudbot_agent:{task_id}"
        task_data_str = self.redis_client.get_cache(task_key)

        if not task_data_str:
            logger.warning(f"Task {task_id} not found for status update")
            raise ValueError(f"Task {task_id} not found for status update")
        try:
            task_data = json.loads(task_data_str)
            task_data["status"] = status.value

            if status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]:
                task_data["completed_at"] = datetime.utcnow().isoformat() + "Z"
            if result is not None:
                task_data["data"]["result"] = result
            if detail is not None:
                task_data["data"]["detail"] = detail
            if error is not None:
                task_data["data"]["error"] = error
            # 更新Redis中的数据
            self.redis_client.set_cache(task_key, json.dumps(task_data), ttl_seconds=24 * 3600)
            logger.info(f"Updated task {task_id} status to {status.value}")

        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to update task {task_id}: {e}")


