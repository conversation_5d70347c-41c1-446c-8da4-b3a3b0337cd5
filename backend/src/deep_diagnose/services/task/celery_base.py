import asyncio
import logging

from celery import Celery
from celery.signals import worker_init

from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)

celery = Celery(
    "ecs-deep-diagnose",
    broker=get_config().infrastructure.redis.broker_url,
    backend=get_config().infrastructure.redis.result_backend_url,
    include=[
        "deep_diagnose.services.task.celery_tasks",
        "deep_diagnose.tools.mcp.celery_tasks",  # 添加MCP任务模块
    ],
)  # 创建Celery对象
celery.config_from_object("deep_diagnose.services.task.celery_config")


@worker_init.connect
def init_worker(**kwargs):
    """Celery worker 启动时初始化数据库"""
    logger.info("🚀 Initializing Celery worker...")
    try:
        # 检查是否已有事件循环
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
            logger.info("Using existing event loop for worker initialization")
        except RuntimeError:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            logger.info("Created new event loop for worker initialization")
        
        from deep_diagnose.data.database import db_manager
        loop.run_until_complete(db_manager.init_database())
        logger.info("✅ Database initialized for Celery worker")
        
        # 保持事件循环开放，供后续任务使用
        logger.info(f"Worker event loop ready: {id(loop)}")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize database for Celery worker: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        # 不要抛出异常，让worker继续运行
        logger.warning("⚠️ Worker will continue without database initialization")


# @worker_shutdown.connect
def shutdown_worker(**kwargs):
    """Celery worker 关闭时清理数据库连接"""
    logger.info("🛑 Shutting down Celery worker...")
    try:
        from deep_diagnose.data.database import db_manager
        
        # 尝试优雅地关闭数据库连接
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
            
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                task = asyncio.create_task(db_manager.close_database())
                # 等待任务完成（在关闭时应该很快）
                loop.run_until_complete(asyncio.wait_for(task, timeout=5.0))
            else:
                # 如果事件循环未运行，直接运行
                loop.run_until_complete(db_manager.close_database())
                
        except (RuntimeError, asyncio.TimeoutError) as e:
            logger.warning(f"Could not use existing event loop for shutdown: {e}")
            # 如果没有事件循环或超时，创建新的
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(db_manager.close_database())
                loop.close()
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup with new event loop: {cleanup_error}")
                
        logger.info("✅ Database connections closed for Celery worker")
        
    except Exception as e:
        logger.error(f"❌ Failed to close database connections for Celery worker: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")


# 确保任务模块被导入
