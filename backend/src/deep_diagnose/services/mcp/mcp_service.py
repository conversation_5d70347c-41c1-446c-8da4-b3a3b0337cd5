"""
MCP服务

提供MCP工具相关的业务逻辑
"""

import logging

# Moved import inside the class to avoid circular imports
from deep_diagnose.api.models.mcp_response import (
    MCPToolsResponse, MCPRefreshResponse, MCPDescriptionResponse,
    MCPToolsData, MCPRefreshData, MCPDescriptionData,
    MCPServerInfo, MCPToolInfo
)

logger = logging.getLogger(__name__)


class MCPService:
    """MCP工具服务"""
    
    def __init__(self):
        from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager
        self.cache_manager = MCPCacheManager()
    
    async def get_tools(self) -> MCPToolsResponse:
        """
        获取所有MCP工具信息
        
        Returns:
            MCPToolsResponse: 包含工具信息的响应模型
        """
        try:
            logger.info("Getting MCP tools from service")
            
            # 从缓存管理器获取工具
            tools_dict = await self.cache_manager.get_tools()
            
            if tools_dict:
                # 统计信息
                total_tools = 0
                servers_info = {}
                
                for server_name, tools in tools_dict.items():
                    tool_list = []
                    for tool in tools:
                        tool_info = MCPToolInfo(
                            name=tool.name,
                            description=tool.description,
                            args_schema=tool.args if hasattr(tool, 'args') else None
                        )
                        tool_list.append(tool_info)
                    
                    servers_info[server_name] = MCPServerInfo(
                        tools=tool_list,
                        tool_count=len(tools)
                    )
                    total_tools += len(tools)
                
                data = MCPToolsData(
                    servers=servers_info,
                    total_servers=len(tools_dict),
                    total_tools=total_tools
                )
                
                logger.info(f"Retrieved {total_tools} tools from {len(tools_dict)} servers")
                return MCPToolsResponse(success=True, data=data)
            else:
                logger.warning("No MCP tools found")
                data = MCPToolsData(servers={}, total_servers=0, total_tools=0)
                return MCPToolsResponse(success=True, data=data)
            
        except Exception as e:
            logger.error(f"Error getting MCP tools: {e}")
            return MCPToolsResponse(success=False, error=str(e), data=None)
    
    async def refresh_tools(self) -> MCPRefreshResponse:
        """
        刷新MCP工具缓存
        
        Returns:
            MCPRefreshResponse: 刷新结果响应模型
        """
        try:
            logger.info("Refreshing MCP tools cache")
            
            # 刷新缓存
            tools_dict = await self.cache_manager.refresh_tools()
            
            # 统计刷新结果
            total_tools = sum(len(tools) for tools in tools_dict.values()) if tools_dict else 0
            total_servers = len(tools_dict) if tools_dict else 0
            
            message = f"Successfully refreshed {total_tools} tools from {total_servers} servers"
            data = MCPRefreshData(
                total_servers=total_servers,
                total_tools=total_tools,
                servers={
                    server_name: len(tools) 
                    for server_name, tools in tools_dict.items()
                } if tools_dict else {}
            )
            
            logger.info(f"Cache refresh completed: {total_tools} tools from {total_servers} servers")
            return MCPRefreshResponse(success=True, message=message, data=data)
            
        except Exception as e:
            logger.error(f"Error refreshing MCP tools: {e}")
            return MCPRefreshResponse(
                success=False,
                error=str(e),
                message="Failed to refresh MCP tools cache",
                data=None
            )
    
    async def get_tools_description(self) -> MCPDescriptionResponse:
        """
        获取MCP工具的Markdown描述
        
        Returns:
            MCPDescriptionResponse: 包含工具描述的响应模型
        """
        try:
            from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
            
            logger.info("Getting MCP tools description")
            
            tool_manager = MCPToolManager()
            description = await tool_manager.get_enabled_mcp_tools_description()
            
            data = MCPDescriptionData(
                description=description,
                format="markdown"
            )
            
            return MCPDescriptionResponse(success=True, data=data)
            
        except Exception as e:
            logger.error(f"Error getting MCP tools description: {e}")
            return MCPDescriptionResponse(success=False, error=str(e), data=None)