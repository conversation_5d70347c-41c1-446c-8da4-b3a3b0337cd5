"""
Token service module for authentication
"""
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any

from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)


class TokenService:
    """Token service for handling authentication and token generation"""
    
    def __init__(self):
        self.config = get_config()
        self._db_initialized = False
    
    def _is_database_auth_enabled(self) -> bool:
        """Check if database authentication is enabled"""
        return getattr(self.config.auth, 'enable_database_auth', False)
    
    async def _authenticate_with_yaml(self, username: str, password: str) -> bool:
        """Authenticate user with YAML configuration"""
        try:
            local_users = self.config.auth.jwt_users
            for user in local_users:
                if user["user_name"] == username and user["password"] == password:
                    logger.info(f"YAML authentication successful for user: {username}")
                    return True
            logger.warning(f"YAML authentication failed for user: {username}")
            return False
        except Exception as e:
            logger.error(f"Error during YAML authentication: {e}")
            return False
    
    async def _ensure_database_connection(self):
        """Ensure database connection is initialized"""
        if not self._db_initialized:
            try:
                from deep_diagnose.data.database import init_db
                await init_db()
                self._db_initialized = True
                logger.info("Database connection initialized for token service")
            except Exception as e:
                logger.error(f"Failed to initialize database connection: {e}")
                raise

    async def _authenticate_with_database(self, username: str, password: str) -> bool:
        """Authenticate user with database"""
        try:
            # Ensure database connection is available
            #await self._ensure_database_connection()
            
            # Import here to avoid circular imports and ensure DB is ready
            from deep_diagnose.domain.user.repository import user_crud
            
            user = await user_crud.verify_password(username, password)
            if user:
                logger.info(f"Database authentication successful for user: {username}")
                return True
            else:
                logger.warning(f"Database authentication failed for user: {username}")
                return False
        except Exception as e:
            logger.error(f"Error during database authentication: {e}")
            return False
    
    async def authenticate_user(self, username: str, password: str) -> bool:
        """
        Authenticate user using the configured authentication method
        
        Args:
            username: Username for authentication
            password: Password for authentication
            
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Check if database authentication is enabled
            if self._is_database_auth_enabled():
                logger.info(f"Using database authentication for user: {username}")
                return await self._authenticate_with_database(username, password)
            else:
                logger.info(f"Using YAML authentication for user: {username}")
                return await self._authenticate_with_yaml(username, password)
        except Exception as e:
            logger.error(f"Authentication error for user {username}: {e}")
            return False
    
    def generate_token(self, username: str, token_lifetime_minutes: Optional[int] = None) -> str:
        """
        Generate JWT token for authenticated user
        
        Args:
            username: Username for token generation
            token_lifetime_minutes: Token lifetime in minutes (optional)
            
        Returns:
            str: Generated JWT token
        """
        try:
            # Import here to avoid circular imports
            from deep_diagnose.security.auth.auth_utils import create_jwt_access_token
            from deep_diagnose.security.auth.constants import ACCESS_TOKEN_EXPIRE_MINUTES
            
            expires_minutes = token_lifetime_minutes or ACCESS_TOKEN_EXPIRE_MINUTES
            access_token_expires = timedelta(minutes=expires_minutes)
            
            token_data = {"sub": username}
            access_token = create_jwt_access_token(
                data=token_data, 
                expires_delta=access_token_expires
            )
            
            logger.info(f"Token generated successfully for user: {username}, expires in: {expires_minutes} minutes")
            return access_token
        except Exception as e:
            logger.error(f"Error generating token for user {username}: {e}")
            raise
    
    async def authenticate_and_generate_token(
        self, 
        username: str, 
        password: str, 
        token_lifetime_minutes: Optional[int] = None
    ) -> Optional[str]:
        """
        Authenticate user and generate token if successful
        
        Args:
            username: Username for authentication
            password: Password for authentication
            token_lifetime_minutes: Token lifetime in minutes (optional)
            
        Returns:
            Optional[str]: Generated token if authentication successful, None otherwise
        """
        try:
            # Authenticate user
            is_authenticated = await self.authenticate_user(username, password)
            
            if not is_authenticated:
                return None
            
            # Generate token for authenticated user
            token = self.generate_token(username, token_lifetime_minutes)
            return token
            
        except Exception as e:
            logger.error(f"Error in authenticate_and_generate_token for user {username}: {e}")
            return None
    
    def get_authentication_info(self) -> Dict[str, Any]:
        """
        Get current authentication configuration info
        
        Returns:
            Dict[str, Any]: Authentication configuration information
        """
        try:
            from deep_diagnose.security.auth.constants import ACCESS_TOKEN_EXPIRE_MINUTES
        except ImportError:
            ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Default fallback
            
        return {
            "database_auth_enabled": self._is_database_auth_enabled(),
            "yaml_auth_enabled": not self._is_database_auth_enabled(),
            "default_token_lifetime_minutes": ACCESS_TOKEN_EXPIRE_MINUTES
        }


# Global token service instance
token_service = TokenService()