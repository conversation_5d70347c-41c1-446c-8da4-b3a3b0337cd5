# -*- coding: utf-8 -*-
from datetime import timedelta
from typing import Optional

from fastapi import Request

from deep_diagnose.security.auth.buc_auth import BUCAuth
from deep_diagnose.security.auth.constants import WHITELIST_URLS
from deep_diagnose.security.auth.jwt_auth import J<PERSON><PERSON><PERSON>
from deep_diagnose.security.auth.pop_auth import POPAuth
from deep_diagnose.common.config.core.environment import get_environment

def is_prefix_of_whitelist(url):
    """
    检查给定的URL是否为白名单中某个URL的前缀。

    参数:
    url (str): 需要检查的URL字符串。

    返回值:
    bool: 如果给定的URL是白名单中某个URL的前缀，则返回True；否则返回False。
    """
    # 遍历白名单中的URL前缀，检查给定URL是否以其中任何一个前缀开始
    for prefix in WHITELIST_URLS:
        if url.startswith(prefix):
            return True  # 找到匹配的前缀，返回True
    return False  # 没有找到匹配的前缀，返回False



def create_jwt_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    创建JWT访问令牌。

    参数:
    - data: dict, 包含将被编码进JWT的数据字典。
    - expires_delta: Optional[timedelta], 令牌过期时间的偏移量。如果提供，令牌将在这个偏移量的时间内有效。

    返回值:
    - 返回一个通过JWTAuth类的create_token方法生成的JWT访问令牌。
    """
    return JWTAuth().create_token(data, expires_delta)



# 定义一个解析jwt token的函数
def decode_jwt_access_token(token: str):
    """
    解码JWT访问令牌

    参数:
    token (str): 需要解码的JWT访问令牌字符串

    返回值:
    返回解码后的令牌信息，如果验证失败则返回None
    """
    return JWTAuth().verify_token(token)



async def authenticate(request):
    """
    异步认证函数，用于对请求进行身份验证。

    参数:
    - request: 请求对象，包含请求的详细信息。

    返回值:
    - 返回True表示认证成功，返回False表示认证失败。
    """
    # 检查请求路径是否在白名单中或白名单路径的前缀
    # if get_environment() not in ('prod','pre'):
    #     return True
    if is_prefix_of_whitelist(request.url.path):
        return True
    else:
        # 尝试使用BUCAuth进行认证
        buc_auth = await BUCAuth().authenticate(request)
        if buc_auth is False:
            # BUCAuth认证失败，尝试使用JWTAuth进行认证
            jwt_authentication = await JWTAuth().authenticate(request)
            if jwt_authentication == False and buc_auth == False:
                # JWTAuth认证也失败，最后尝试使用POPAuth进行认证
                acs_authentictaion = await POPAuth().authenticate(request)
                if acs_authentictaion is False:
                    # 所有认证方式都失败，返回False
                    return False
            else:
                # JWTAuth认证成功，或之前BUCAuth认证成功，返回True
                return True



async def get_user(request: Request):
    """
    异步获取用户信息。

    尝试通过多种认证方式获取用户信息，包括BUCAuth、JWTAuth和POPAuth。
    如果都能找到用户信息，则返回该用户信息；如果都无法找到，则返回None。

    参数:
    - request: Request对象，表示客户端的请求。

    返回值:
    - 用户信息对象，如果无法获取用户信息则返回None。
    """
    # 尝试通过BUCAuth认证获取用户信息
    buc_user = await BUCAuth().get_user(request)
    if buc_user:
        return buc_user
    # 如果BUCAuth认证失败，尝试通过JWTAuth认证获取用户信息
    jwt_user = await JWTAuth().get_user(request)
    if jwt_user:
        return jwt_user
    # 如果JWTAuth认证也失败，尝试通过POPAuth认证获取用户信息
    pop_user = await POPAuth().get_user(request)
    if pop_user:
        return pop_user
    else:
        return None

