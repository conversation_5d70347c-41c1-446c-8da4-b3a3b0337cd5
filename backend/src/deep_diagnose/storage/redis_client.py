import redis
from redis import Redis
from redis import asyncio as aioredis
from typing import Optional

from deep_diagnose.common.config import get_config


class RedisClient:
    def __init__(
            self,
            host=get_config().infrastructure.redis.host,
            port=get_config().infrastructure.redis.port,
            username=get_config().infrastructure.redis.user,
            password=get_config().infrastructure.redis.password
    ):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        # Persistent clients to avoid reconnect overhead per call
        self._client: Optional[Redis] = None
        self._async_client: Optional[aioredis.Redis] = None

    def _get_client(self) -> Redis:
        if self._client is None:
            self._client = redis.StrictRedis(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                decode_responses=True,
            )
        return self._client

    def _get_async_client(self) -> aioredis.Redis:
        if self._async_client is None:
            self._async_client = aioredis.Redis(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                decode_responses=True,
            )
        return self._async_client

    # Synchronous API (kept for backward compatibility)
    def get_cache_keys(self, pattern):
        client = self._get_client()
        return client.keys(pattern)

    def set_cache(self, key, value, ttl_seconds=3600):
        client = self._get_client()
        client.setex(key, ttl_seconds, value)

    def get_cache(self, key):
        client = self._get_client()
        return client.get(key)

    def delete_cache(self, key):
        client = self._get_client()
        client.delete(key)

    # Async API to prevent blocking event loops in async paths
    async def set_cache_async(self, key, value, ttl_seconds=3600):
        client = self._get_async_client()
        await client.setex(key, ttl_seconds, value)

    async def get_cache_async(self, key):
        client = self._get_async_client()
        return await client.get(key)

    async def delete_cache_async(self, key):
        client = self._get_async_client()
        await client.delete(key)

    # --- Pub/Sub helpers for low-latency streaming ---
    async def publish_async(self, channel: str, message: str) -> int:
        """Publish a message to a channel. Returns number of clients that received the message."""
        client = self._get_async_client()
        return await client.publish(channel, message)

    async def get_pubsub(self):
        """Get an async PubSub object from the async client."""
        client = self._get_async_client()
        return client.pubsub()
