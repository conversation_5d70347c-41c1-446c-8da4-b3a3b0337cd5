import oss2  # 阿里云OSS SDK
from deep_diagnose.common.config import get_config


class OssClient:
    def __init__(
            self,
            endpoint=get_config().infrastructure.oss.endpoint,
            bucket_name=get_config().infrastructure.oss.bucket_name,
            region=get_config().infrastructure.oss.region,
            access_key=get_config().infrastructure.oss.access_key_id,
            secret_key=get_config().infrastructure.oss.access_key_secret,
    ):
        self.endpoint = endpoint
        self.bucket_name = bucket_name
        self.region = region
        self.access_key = access_key
        self.secret_key = secret_key

    def _get_bucket(self):
        auth = oss2.Auth(self.access_key, self.secret_key)
        return oss2.Bucket(auth, self.endpoint, self.bucket_name, region=self.region)

    def upload_file(self, file_path: str, oss_path: str):
        bucket = self._get_bucket()
        bucket.put_object_from_file(oss_path, file_path)

    def download_file(self, oss_path: str, file_path: str):
        bucket = self._get_bucket()
        bucket.get_object_to_file(oss_path, file_path)

    def delete_file(self, oss_path: str):
        bucket = self._get_bucket()
        bucket.delete_object(oss_path)

    def get_file_url(self, oss_path: str, expires_seconds: int = 3600):
        bucket = self._get_bucket()
        return bucket.sign_url('GET', oss_path, expires_seconds)
