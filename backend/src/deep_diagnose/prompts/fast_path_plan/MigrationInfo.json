[{"id": 1, "tool": "listLiveMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的热迁移记录。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 2, "tool": "listColdMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的冷迁移记录。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}]