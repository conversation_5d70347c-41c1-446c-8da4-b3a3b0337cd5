[{"id": 1, "tool": "listColdMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围${start_time} 至 ${end_time}", "analysis": "检查在指定时间范围内是否发生过冷迁移。宕机可能伴随着冷迁移，可以尝试确定宕机发生的时间。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 2, "tool": "listMonitorExceptions", "dependence": [], "input": "实例ID ${vm}和时间范围${start_time} 至 ${end_time}", "analysis": "查询指定时间段内该实例是否存在与主动运维（判断与主动运维的关联）和VM内部操作（判断是否内部行为导致）相关的监控项。", "fastParams": {"machineId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 3, "tool": "listActionTrail", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的控制台操作记录，判断是否是客户自行操作重启，但误认为宕机", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 4, "tool": "listReportedOperationalEvents", "dependence": [], "input": "实例ID ${vm}和时间范围${start_time} 至 ${end_time}", "analysis": "查找相关的客户侧事件通知。如果存在状态为已执行的计划运维事件，则重启可能是该事件执行导致的；如果存在状态为已响应的计划运维事件，则重启可能是为了响应该事件而进行的；如果存在非预期运维事件，则重启可能是由于非预期的故障，此事件仅起到通知作用。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 5, "tool": "listOperationRuleMatchRecords", "dependence": [], "input": "实例ID ${vm}和时间范围${start_time} 至 ${end_time}", "analysis": "查找相关的运维规则命中记录，尝试确定宕机原因。", "fastParams": {"machineId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 6, "tool": "runDiagnose", "dependence": [], "input": "实例ID ${vm}和时间范围${start_time} 至 ${end_time}及诊断类型VM可用性问题", "analysis": "对实例进行可用性诊断，以发现可能引起非预期宕机的具体原因。", "fastParams": {"machineId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}", "type": "REASON_VM_APPLICABILITY"}}]