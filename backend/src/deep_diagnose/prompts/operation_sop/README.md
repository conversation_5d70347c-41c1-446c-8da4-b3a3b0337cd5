# Operation SOP 目录

本目录只包含纯粹的SOP（标准操作程序）内容，用于动态诊断规划。

## 目录结构

- `operation_sop.yaml` - SOP配置文件，定义所有可用的诊断策略
- `*.md` - 具体的SOP内容文件，包含详细的诊断步骤和指导

## SOP配置格式

```yaml
operation_sops:
  - name: "策略名称"
    file_name: "对应的md文件名"
    example_questions:
      - "示例问题1"
      - "示例问题2"
    tools:
      - "工具1"
      - "工具2"
    description: "策略描述"
    keywords:
      - "关键词1"
      - "关键词2"
    priority: 1
```

## 动态特性

- **数量可变**: 支持任意数量的SOP策略
- **类型灵活**: 不限制特定的策略类型
- **热重载**: 支持运行时重新加载配置
- **LLM驱动**: 基于LLM智能选择合适的SOP

## 规划能力

SOP的规划能力已迁移到 `planning/` 目录：
- `planning/strategy_selector.py` - 动态策略选择器
- `planning/sop_planner.py` - SOP规划器

## 使用方式

```python
from planning import select_operation_sop, plan_with_dynamic_sop

# 选择SOP
sop_content, reason = select_operation_sop("用户问题")

# 完整规划
result = plan_with_dynamic_sop("用户问题")
```