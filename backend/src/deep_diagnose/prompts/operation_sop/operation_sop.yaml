operation_sops:
  - name: "批量实例不可用诊断方案"
    file_name: "sop_batch_instance_unavailable.md"
    example_questions:
      - "实例i-xxx、i-yyy、i-zzz、i-aaa同时不可用"
      - "批量实例在凌晨2点都出现了连接问题"
      - "这12个实例都无法访问"
      - "多个实例同时出现故障"
    tools:
      - "listVmHostHistory"
      - "runVmUnavailableDiagnose"
      - "listChangeRecords"

  - name: "单实例重启或宕机诊断方案"
    file_name: "sop_single_instance_restart.md"
    example_questions:
      - "实例i-xxx异常重启了"
      - "服务器突然宕机"
      - "实例自动重启，不知道什么原因"
      - "单个实例无响应"
    tools:
      - "getVmBasicInfo"
      - "listMonitorExceptions"
      - "listOperationRecords"
      - "listChangeRecords"
      - "listActionTrail"
      - "listReportedOperationalEvents"
      - "get_vm_coredump"
      - "runDiagnose"

  - name: "物理机宕机分析方案"
    file_name: "sop_physical_host_crash_analysis.md"
    example_questions:
      - "基于vmcore分析NC(***********)的宕机根因"
      - "通过内核、虚拟化、金轮分析物理机111.111.11的宕机根因"
      - "NC宕机vmcore诊断"
    tools:
      - "getCrashLog"
      - "analyzeVmcoreFromCore"
      - "analyzeVmcoreFromVirt"
      - "analyzeVmcoreFromJinlun"
      - "listVmsOnNc"
      - "listChangeRecords"

  - name: "其他问题"
    file_name: "default.md"
    example_questions:
      - "网络连接问题"
      - "性能问题"
      - "配置问题"
      - "实例规格"
    tools: []