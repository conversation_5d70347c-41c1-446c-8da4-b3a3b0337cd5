# SOP：实例重启或宕机诊断方案

## 适用场景
适用于单个实例或少量实例（通常1-3个）的重启、宕机问题诊断。

## 识别规则
如果用户问题包含以下特征，必须使用此诊断方案：
- 单个或少量实例异常重启
- 实例突然宕机或无响应
- 实例自动重启原因不明
- 非批量性的实例可用性问题

## 诊断步骤（必须按顺序执行）

### 步骤1：获取实例基本信息
**目标**：获取实例基础信息，确定所在物理机
- **工具**：`getVmBasicInfo`
- **输入参数**：实例ID
- **关键输出内容**：
  - 实例基本信息：规格、状态、创建时间
  - 所在物理机：NC IP地址
  - 实例当前状态：运行中/已停止/其他
  - 实例配置信息：CPU、内存、磁盘等

### 步骤2：分析物理机异常事件
**目标**：检查实例所在物理机是否存在异常事件
- **工具**：`listMonitorExceptions`
- **输入参数**：NC IP（从步骤1获取），时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查NC是否有宕机事件
  - 分析硬件故障告警
  - 评估NC异常与实例问题的关联性
- **关键输出内容**：
  - NC异常事件总数：X条异常
  - 异常事件类型：宕机/硬件故障/网络异常/其他
  - 异常时间与实例问题时间关联：异常时间 vs 问题时间
  - NC层面影响评估：是否影响实例运行

### 步骤3：分析物理机运维记录
**目标**：检查物理机是否有运维操作影响实例
- **工具**：`listOperationRecords`
- **输入参数**：NC IP（从步骤1获取），时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查NC重启或维护操作
  - 分析运维时间与实例问题的关联性
  - 评估运维操作对实例的影响
- **关键输出内容**：
  - NC运维记录总数：X条记录
  - 运维操作类型：重启/维护/升级/其他
  - 运维时间与实例问题时间关联：运维时间 vs 问题时间
  - 运维影响评估：是否导致实例重启或宕机

### 步骤4：分析物理机变更记录
**目标**：检查物理机配置或硬件变更
- **工具**：`listChangeRecords`
- **输入参数**：NC IP（从步骤1获取），时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查NC硬件或配置变更
  - 分析变更时间与实例问题的关联性
  - 评估变更对实例稳定性的影响
- **关键输出内容**：
  - NC变更记录总数：X条变更
  - 变更类型：硬件变更/配置变更/系统更新/其他
  - 变更时间与实例问题时间关联：变更时间 vs 问题时间
  - 变更影响评估：是否可能导致实例问题

### 步骤5：分析实例异常事件
**目标**：检查实例层面是否有异常事件记录
- **工具**：`listMonitorExceptions`
- **输入参数**：实例ID，时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查实例重启或宕机记录
  - 分析实例层面的异常告警
  - 评估异常事件与问题的直接关联性
- **关键输出内容**：
  - 实例异常事件总数：X条异常
  - 异常事件类型：重启/宕机/性能异常/其他
  - 异常时间与问题时间关联：异常时间 vs 问题时间
  - 实例层面影响评估：异常事件的严重程度和影响

### 步骤6：分析控制台操作记录
**目标**：检查是否有人为的控制台操作导致实例重启
- **工具**：`listActionTrail`
- **输入参数**：实例ID，时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查控制台重启、停止、启动操作
  - 分析操作时间与问题时间的关联性
  - 识别操作用户和操作来源
- **关键输出内容**：
  - 控制台操作总数：X条操作
  - 操作类型：重启/停止/启动/配置变更/其他
  - 操作时间与问题时间关联：操作时间 vs 问题时间
  - 操作来源分析：用户操作/系统自动操作

### 步骤7：分析客户侧运维事件
**目标**：检查是否有计划性的运维事件通知
- **工具**：`listReportedOperationalEvents`
- **输入参数**：实例ID，时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查计划性维护通知
  - 分析运维事件与实例问题的关联性
  - 评估是否为预期的运维操作
- **关键输出内容**：
  - 运维事件总数：X条事件
  - 事件类型：计划维护/紧急维护/系统升级/其他
  - 事件时间与问题时间关联：事件时间 vs 问题时间
  - 运维事件影响评估：是否为计划内操作

### 步骤8：分析实例内核崩溃
**目标**：检查实例操作系统内核是否发生崩溃
- **工具**：`get_vm_coredump`
- **输入参数**：实例ID，时间范围（问题发生前后2小时）
- **分析要点**：
  - 检查内核崩溃转储文件
  - 分析崩溃原因和调用栈
  - 评估内核问题与实例重启的关联性
- **关键输出内容**：
  - 内核崩溃状态：有崩溃/无崩溃
  - 崩溃类型：panic/oops/其他（如有）
  - 崩溃原因分析：具体错误信息和可能原因
  - 内核层面结论：是否为内核问题导致的重启

### 步骤9：通用诊断分析
**目标**：执行综合诊断，获取实例整体健康状态
- **工具**：`runDiagnose`
- **输入参数**：实例ID
- **分析要点**：
  - 获取实例整体健康评估
  - 分析性能指标和资源使用情况
  - 识别潜在的稳定性风险
- **关键输出内容**：
  - 诊断总体结论：健康/异常/风险
  - 性能指标分析：CPU、内存、磁盘、网络状态
  - 发现的问题：具体问题描述和影响评估
  - 建议措施：优化建议和风险规避方案
