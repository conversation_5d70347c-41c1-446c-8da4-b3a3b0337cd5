"""
简化的SOP管理器 - 只用LLM选择策略
"""
import json
import logging
from typing import Dict, Any, Tuple
from .sop_query import get_sop_query

logger = logging.getLogger(__name__)


class SOPManager:
    """简化的SOP管理器 - 复用sop_query功能"""
    
    def __init__(self):
        self.sop_query = get_sop_query()
    
    def process(self, query: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理查询并注入状态"""
        try:
            content, name, reason = self._select_and_load(query)
            
            # 注入状态 - 使用planner.md期望的状态键
            state["operation_sop_content"] = content
            state["sop_name"] = name
            state["sop_reason"] = reason
            
            # 如果state是ReasoningState对象，也设置其sop_name属性
            if hasattr(state, 'sop_name'):
                state.sop_name = name
            
            logger.info(f"SOP处理成功: {name}")
            return {"success": True, "content": content, "name": name}
            
        except Exception as e:
            error_msg = f"SOP处理失败: {str(e)}"
            logger.error(error_msg)
            
            state["operation_sop_content"] = ""
            state["sop_name"] = ""
            state["sop_reason"] = error_msg
            
            # 如果state是ReasoningState对象，也设置其sop_name属性
            if hasattr(state, 'sop_name'):
                state.sop_name = ""
            
            return {"success": False, "error": error_msg}
    
    def get_content_only(self, query: str) -> str:
        """仅获取SOP内容"""
        try:
            content, _, _ = self._select_and_load(query)
            return content
        except Exception as e:
            logger.error(f"获取SOP内容失败: {e}")
            return ""
    
    def inject_sop_to_state(self, user_query: str, state: Dict[str, Any]):
        """兼容性方法 - 返回兼容的结果对象"""
        result = self.process(user_query, state)
        
        # 创建兼容的结果对象
        class CompatResult:
            def __init__(self, success, sop_content, selection_reason, error_message=None):
                self.success = success
                self.sop_content = sop_content
                self.selection_reason = selection_reason
                self.error_message = error_message
        
        return CompatResult(
            success=result.get("success", False),
            sop_content=result.get("content", ""),
            selection_reason=result.get("name", ""),
            error_message=result.get("error")
        )
    
    def get_sop_content(self, user_query: str) -> tuple:
        """兼容性方法 - 返回元组"""
        content = self.get_content_only(user_query)
        return content, "LLM选择"
    
    def _select_and_load(self, query: str) -> Tuple[str, str, str]:
        """选择并加载SOP"""
        configs = self.sop_query.get_configs()
        selected, reason = self._select_sop(query, configs)
        content = self.sop_query.load_sop_content(selected.get("file_name", ""))
        name = selected.get("name", "未知SOP")
        return content, name, reason
    
    def _select_sop(self, query: str, configs) -> Tuple[Dict, str]:
        """LLM选择SOP"""
        if not configs:
            return {}, "没有可用配置"
        
        try:
            # 构建选择提示
            options = []
            for i, config in enumerate(configs):
                name = config.get('name', f'选项{i}')
                examples = config.get('example_questions', [])
                example_text = ', '.join(examples[:2]) if examples else '无示例'
                options.append(f"{i}: {name} (示例: {example_text})")
            
            prompt = f"""从以下SOP选项中选择最适合的一个:

用户问题: {query}

可选SOP:
{chr(10).join(options)}

返回JSON: {{"index": 0, "reason": "选择理由"}}"""
            
            # 调用LLM
            from deep_diagnose.llms.llm import get_llm_by_type
            from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
            
            llm = get_llm_by_type(AGENT_LLM_MAP.get("planner", "reasoning"))
            messages = [{"role": "user", "content": prompt}]
            response = llm.invoke(messages, config={"stream": False, "tags": ["silent_llm_call"]})
            
            # 解析结果
            result = self._parse_response(response.content)
            index = result.get("index", 0)
            reason = result.get("reason", "LLM选择")
            
            if 0 <= index < len(configs):
                return configs[index], f"LLM选择: {reason}"
            else:
                return configs[0], f"索引无效，使用默认: {reason}"
                
        except Exception as e:
            logger.error(f"LLM选择失败: {e}")
            return configs[0], f"LLM失败，使用默认: {str(e)}"
    
    def _parse_response(self, content: str) -> Dict:
        """解析LLM响应"""
        try:
            import re
            json_match = re.search(r'\{.*?\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
        except:
            pass
        return {"index": 0, "reason": "解析失败"}
    


# 全局单例
_manager = None

def get_sop_manager() -> SOPManager:
    """获取管理器实例"""
    global _manager
    if _manager is None:
        _manager = SOPManager()
    return _manager

def inject_sop(query: str, state: Dict[str, Any]) -> Dict[str, Any]:
    """便捷函数: 注入SOP"""
    return get_sop_manager().process(query, state)

def get_sop_content(query: str) -> str:
    """便捷函数: 获取内容"""
    return get_sop_manager().get_content_only(query)