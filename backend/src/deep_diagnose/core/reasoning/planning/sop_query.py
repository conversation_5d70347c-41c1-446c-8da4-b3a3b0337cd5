"""
SOP查询功能模块 - 简化版本

提供基础的SOP查询功能
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class SOPQuery:
    """简化的SOP查询功能类"""
    
    def __init__(self, config_path: str = "deep_diagnose/prompts/operation_sop/operation_sop.yaml"):
        self.config_path = Path(config_path)
        self.sop_dir = self.config_path.parent
        self._configs = None
    
    def get_configs(self) -> List[Dict]:
        """获取所有SOP配置"""
        if self._configs is None:
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    self._configs = data.get('operation_sops', [])
            except Exception as e:
                logger.error(f"Failed to load configs: {e}")
                self._configs = []
        return self._configs
    
    def list_sops(self) -> List[str]:
        """查询有哪些SOP"""
        configs = self.get_configs()
        return [config.get("name", "") for config in configs]
    
    def get_sop_tools(self, sop_name: str) -> List[str]:
        """查询SOP需要的工具列表"""
        configs = self.get_configs()
        for config in configs:
            if config.get("name") == sop_name:
                tools = config.get("tools", [])
                return [tool for tool in tools if tool and tool.strip()]
        return []
    
    def get_sop_config(self, sop_name: str) -> Dict[str, Any]:
        """获取SOP配置"""
        configs = self.get_configs()
        for config in configs:
            if config.get("name") == sop_name:
                return config
        return {}
    
    def load_sop_content(self, file_name: str) -> str:
        """加载SOP内容文件"""
        if not file_name or 'planner.md' in file_name:
            return ""
        
        try:
            file_path = self.sop_dir / file_name
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to load content: {e}")
            return ""


# 全局单例
_query_instance = None

def get_sop_query() -> SOPQuery:
    """获取SOP查询实例"""
    global _query_instance
    if _query_instance is None:
        _query_instance = SOPQuery()
    return _query_instance