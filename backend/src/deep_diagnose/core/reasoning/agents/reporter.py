"""
报告Agent - 负责生成最终诊断报告
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from .base import BaseAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template


class ReporterAgent(BaseAgent):
    """报告Agent"""
    
    def __init__(self):
        super().__init__("reporter", "reporter")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None):
        current_plan = state.get("current_plan")
        
        # 准备输入
        input_data = {
            "messages": [
                HumanMessage(f"# 诊断需求 \n\n## 任务\n\n{current_plan.title}\n\n## 描述\n\n{current_plan.thought}")
            ],
            "locale": state.get("locale", "zh-CN"),
        }
        
        # 生成消息
        messages = apply_prompt_template("reporter", input_data)
        
        # 添加格式提醒
        messages.append(
            HumanMessage(
                content="IMPORTANT: 按照prompt格式构建报告，包含：关键要点、概述、详细分析、引用。优先使用Markdown表格展示数据。",
                name="system",
            )
        )
        
        # 添加观察结果
        for observation in state.get("observations", []):
            messages.append(
                HumanMessage(
                    content=f"研究观察结果:\n\n{observation}",
                    name="observation",
                )
            )
        
        # 生成报告
        response = get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke(messages)
        self.logger.info("Report generated successfully")
        
        return {"final_report": response.content}


# 创建实例和节点函数
_reporter = ReporterAgent()

async def reporter_node(state: ReasoningState):
    return await _reporter.execute(state)