"""
Agents module - Clean Agent Architecture

Each Agent has its own file with clear responsibilities:
- coordinator.py - Coordinator Agent
- planner.py - Planner Agent  
- researcher.py - Researcher Agent
- coder.py - Coder Agent
- background_investigator.py - Background Investigator Agent
- human_feedback.py - Human Feedback Agent
- research_team.py - Research Team Agent
- reporter.py - Reporter Agent

Common modules:
- base_graph.py - Agent base classes
- utils.py - Utility functions
"""

from .coordinator import coordinator_node
from .planner import planner_node
from .researcher import researcher_node
from .coder import coder_node
from .background_investigator import background_investigation_node
from .human_feedback import human_feedback_node
from .research_team import research_team_node
from .reporter import reporter_node

__all__ = [
    'coordinator_node',
    'planner_node',
    'researcher_node',
    'coder_node',
    'background_investigation_node',
    'human_feedback_node',
    'research_team_node',
    'reporter_node',
]