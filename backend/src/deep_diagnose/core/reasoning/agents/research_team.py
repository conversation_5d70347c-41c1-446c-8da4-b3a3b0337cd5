"""
研究团队Agent - 负责协调研究任务分配
"""

from typing import Literal, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import BaseAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.prompts.planner_model import StepType


class ResearchTeamAgent(BaseAgent):
    """研究团队Agent"""
    
    def __init__(self):
        super().__init__("research_team", "research_team")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        current_plan = state.get("current_plan")
        if not current_plan or not current_plan.steps:
            return Command(goto="planner")
        
        # 检查是否所有步骤都完成
        if all(step.execution_res for step in current_plan.steps):
            return Command(goto="planner")
        
        # 找到第一个未完成的步骤并路由
        for step in current_plan.steps:
            if not step.execution_res:
                if step.step_type == StepType.PROCESSING:
                    return Command(goto="coder")
                else:  # 默认为research
                    return Command(goto="researcher")
        
        return Command(goto="planner")


# 创建实例和节点函数
_research_team = ResearchTeamAgent()

async def research_team_node(state: ReasoningState) -> Command[Literal["planner", "researcher", "coder"]]:
    return await _research_team.execute(state)