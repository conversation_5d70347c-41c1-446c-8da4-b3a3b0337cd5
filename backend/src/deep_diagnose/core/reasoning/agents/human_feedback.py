"""
人工反馈Agent - 负责处理用户反馈和计划审核
"""

import json
from typing import Literal, Optional
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command, interrupt

from .base import BaseAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.prompts.planner_model import Plan
from deep_diagnose.common.utils.json_utils import repair_json_output


class HumanFeedbackAgent(BaseAgent):
    """人工反馈Agent"""
    
    def __init__(self):
        super().__init__("human_feedback", "human_feedback")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        # 检查是否自动接受
        if state.get("auto_accepted_plan", False):
            self.logger.info("Plan auto-accepted")
            return self._process_accepted_plan(state)
        
        # 获取人工反馈
        feedback = interrupt("Please Review the Plan.")
        return self._handle_feedback(feedback, state)
    
    def _handle_feedback(self, feedback, state) -> Command:
        if not feedback:
            raise TypeError("No feedback provided")
        
        feedback_str = str(feedback).upper()
        
        if feedback_str.startswith("[EDIT_PLAN]"):
            return Command(
                update={"messages": [HumanMessage(content=feedback, name="feedback")]},
                goto="planner",
            )
        elif feedback_str.startswith("[ACCEPTED]"):
            self.logger.info("Plan accepted by user")
            return self._process_accepted_plan(state)
        else:
            raise TypeError(f"Unsupported feedback: {feedback}")
    
    def _process_accepted_plan(self, state) -> Command:
        current_plan = state.get("current_plan", "")
        plan_iterations = state.get("plan_iterations", 0) + 1
        
        try:
            plan_data = json.loads(repair_json_output(current_plan))
            goto = "reporter" if plan_data["has_enough_context"] else "research_team"
            
            return Command(
                update={
                    "current_plan": Plan.model_validate(plan_data),
                    "plan_iterations": plan_iterations,
                    "locale": plan_data["locale"],
                },
                goto=goto,
            )
        except json.JSONDecodeError:
            self.logger.warning("Invalid plan JSON")
            goto = "reporter" if plan_iterations > 0 else "__end__"
            return Command(goto=goto)


# 创建实例和节点函数
_human_feedback = HumanFeedbackAgent()

async def human_feedback_node(state) -> Command[Literal["planner", "research_team", "reporter", "__end__"]]:
    return await _human_feedback.execute(state)