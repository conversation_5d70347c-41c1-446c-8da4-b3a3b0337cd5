"""
协调器Agent - 负责任务分发和客户沟通
"""

from typing import Literal, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command

from .base import BaseAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template


@tool
def handoff_to_planner(
    task_title: Annotated[str, "The title of the task to be handed off."],
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."],
):
    """Handoff to planner agent to do plan."""
    return


class CoordinatorAgent(BaseAgent):
    """协调器Agent"""
    
    def __init__(self):
        super().__init__("coordinator", "coordinator")
    
    def get_default_tools(self):
        return [handoff_to_planner]
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        messages = apply_prompt_template("coordinator", state)
        response = get_llm_by_type(AGENT_LLM_MAP["coordinator"]).bind_tools([handoff_to_planner]).invoke(messages)
        
        if not response.tool_calls:
            self.logger.warning("No tool calls found, terminating workflow")
            return Command(goto="__end__")
        
        # 确定下一步和语言设置
        goto = "background_investigator" if state.get("enable_background_investigation") else "planner"
        locale = self._extract_locale(response.tool_calls, state.get("locale", "zh-CN"))
        
        return Command(update={"locale": locale}, goto=goto)
    
    def _extract_locale(self, tool_calls: list, default_locale: str) -> str:
        try:
            for tool_call in tool_calls:
                if tool_call.get("name") == "handoff_to_planner":
                    if locale := tool_call.get("args", {}).get("locale"):
                        return locale
        except Exception as e:
            self.logger.error(f"Error extracting locale: {e}")
        return default_locale


# 创建实例和节点函数
_coordinator = CoordinatorAgent()

async def coordinator_node(state: ReasoningState) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    return await _coordinator.execute(state)