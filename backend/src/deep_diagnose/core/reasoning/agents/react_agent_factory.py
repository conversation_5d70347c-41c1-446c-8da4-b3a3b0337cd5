"""
Agent工具函数 - 纯粹的公共工具，不包含业务逻辑
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import List, Any, Tuple
from langchain_core.messages import ToolMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.common.config.constants.workflow import DEFAULT_MAX_RECURSIVE_NUM
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


async def setup_mcp_tools(agent_type: str, config: RunnableConfig) -> List:
    """为指定agent设置MCP工具 - 纯工具函数"""
    try:
        mcp_tool_manager = MCPToolManager()
        # 使用新的MCP服务获取过滤后的工具
        filtered_tools = await mcp_tool_manager.get_enabled_mcp_tools()
        logger.info(f"Loaded {len(filtered_tools)} MCP tools for {agent_type}")
        return filtered_tools
    except Exception as e:
        logger.error(f"Failed to setup MCP tools for {agent_type}: {e}")
        return []


async def create_agent_with_tools(agent_type: str, tools: List, config: RunnableConfig):
    """创建带工具的agent - 支持并发工具调用，按阶段切换提示词"""

    configurable = Configuration.from_runnable_config(config)
    mcp_tool_manager = MCPToolManager()

    def _is_empty_tool_payload(obj: Any) -> bool:
        """判断工具返回是否为空/无效数据。支持常见字段：data/items/result/content。"""
        try:
            if obj is None:
                return True
            if isinstance(obj, str):
                return len(obj.strip()) == 0
            if isinstance(obj, (list, tuple, set, dict)) and len(obj) == 0:
                return True
            if isinstance(obj, dict):
                # 只要有一个关键字段存在且为空，则视为空
                for key in ("data", "items", "result", "content"):
                    if key in obj:
                        val = obj.get(key)
                        if val in (None, ""):
                            return True
                        if isinstance(val, (list, tuple, set, dict)) and len(val) == 0:
                            return True
            return False
        except Exception:
            return False

    def _analyze_tool_messages(messages: List[BaseMessage], max_items: int = 6) -> dict:
        """提取最近的 ToolMessage 证据并分析是否为空，返回 {text, has_empty, empty_count}。"""
        result = {"text": "", "has_empty": False, "empty_count": 0}
        if not messages:
            return result
        lines: List[str] = []
        count = 0
        for msg in reversed(messages):
            if isinstance(msg, ToolMessage):
                raw = str(msg.content) if msg.content is not None else ""
                is_empty = False
                # 解析 JSON 判断
                try:
                    import json
                    parsed = json.loads(raw)
                    is_empty = _is_empty_tool_payload(parsed)
                except Exception:
                    # 非 JSON 时，空串也算空
                    is_empty = len(raw.strip()) == 0
                # 截断展示
                display = raw
                if len(display) > 1200:
                    display = display[:1200] + "..."
                prefix = "[EMPTY] " if is_empty else ""
                lines.append(f"- {prefix}tool_call_id={getattr(msg, 'tool_call_id', '')}: {display}")
                if is_empty:
                    result["has_empty"] = True
                    result["empty_count"] += 1
                count += 1
                if count >= max_items:
                    break
        result["text"] = "\n".join(reversed(lines))
        return result

    class Phase(str, Enum):
        SELECT = "select"
        FINAL = "final"

    @dataclass
    class PhaseDecision:
        phase: Phase
        last_tool_output_empty: bool = False
        last_tool_empty_count: int = 0

    def _get_trailing_tool_messages(messages: List[BaseMessage]) -> List[ToolMessage]:
        """获取结尾连续的 ToolMessage 列表（处理并行工具时多个 ToolMessage 会连续出现）。"""
        trailing: List[ToolMessage] = []
        if not messages:
            return trailing
        for m in reversed(messages):
            if isinstance(m, ToolMessage):
                trailing.append(m)
            else:
                break
        return list(reversed(trailing))

    def _detect_phase(agent_state: dict[str, Any]) -> PhaseDecision:
        """基于消息尾部的工具结果，输出清晰的数据结构用于后续逻辑。
        规则：
        - 尾部无 ToolMessage：Phase.SELECT
        - 尾部 ToolMessage 全为空：Phase.SELECT，且 last_tool_output_empty=True
        - 尾部内存在非空：Phase.FINAL
        """
        msgs = agent_state.get("messages") or []
        trailing_tools = _get_trailing_tool_messages(msgs)
        if not trailing_tools:
            return PhaseDecision(Phase.SELECT)
        # 分析尾部工具结果
        empty_count = 0
        total = 0
        try:
            import json
        except Exception:
            json = None
        for tm in trailing_tools:
            total += 1
            raw = str(tm.content) if tm.content is not None else ""
            is_empty = False
            if json:
                try:
                    parsed = json.loads(raw)
                    is_empty = _is_empty_tool_payload(parsed)
                except Exception:
                    is_empty = len(raw.strip()) == 0
            else:
                is_empty = len(raw.strip()) == 0
            if is_empty:
                empty_count += 1
        if total > 0 and empty_count == total:
            return PhaseDecision(Phase.SELECT, last_tool_output_empty=True, last_tool_empty_count=empty_count)
        return PhaseDecision(Phase.FINAL, last_tool_output_empty=False, last_tool_empty_count=empty_count)

    async def generate_prompt(agent_state):
        # 根据SOP获取工具列表过滤描述（仅在工具选择阶段注入，避免答复阶段过度干扰）
        from deep_diagnose.core.reasoning.planning.sop_query import get_sop_query
        sop_query = get_sop_query()
        sop_name = agent_state.get("sop_name", "")
        tool_names = sop_query.get_sop_tools(sop_name) if sop_name else None

        decision = _detect_phase(agent_state)
        if decision.phase == Phase.SELECT:
            agent_state["mcp_servers_description"] = await mcp_tool_manager.get_enabled_mcp_tools_description(tool_names)
            # 若最近的工具全部为空，则提示在选择阶段引导“参数修正/替代工具”
            agent_state["last_tool_output_empty"] = decision.last_tool_output_empty
            agent_state["last_tool_empty_count"] = decision.last_tool_empty_count
            prompt_name = f"{agent_type}_select"
        else:
            # 准备近期工具证据（并检测是否存在空数据）
            analysis = _analyze_tool_messages(agent_state.get("messages", []))
            agent_state["recent_tool_evidence"] = analysis.get("text", "")
            agent_state["has_empty_tool_output"] = analysis.get("has_empty", False)
            agent_state["empty_tool_output_count"] = analysis.get("empty_count", 0)
            prompt_name = f"{agent_type}_final"

        if tool_names:
            logger.info(f"Agent '{agent_type}' using SOP '{sop_name}' tools: {tool_names}")

        # 在应用模板时注入 configurable（之前未使用）
        return apply_prompt_template(prompt_name, agent_state, configurable)

    # 获取 LLM 并启用并发工具调用
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])

    # 确保 LLM 支持并发工具调用
    if hasattr(llm, 'parallel_tool_calls'):
        llm.parallel_tool_calls = True
    elif hasattr(llm, 'bind'):
        # 使用 bind 方法启用并发工具调用
        llm = llm.bind(parallel_tool_calls=True)

    return create_react_agent(
        name=agent_type,
        model=llm,
        tools=tools,
        prompt=generate_prompt,
    )


def get_recursion_limit() -> int:
    """获取递归限制配置 - 纯工具函数"""
    default_limit = DEFAULT_MAX_RECURSIVE_NUM
    return default_limit
    
