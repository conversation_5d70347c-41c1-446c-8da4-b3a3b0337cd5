"""
计划器消息处理器

处理计划器Agent的消息，重命名自 PlannerEventProcessor
"""

import json
import re
import logging
from typing import Dict, Any, List


from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage
from deep_diagnose.common.utils.json_utils import extract_json_code_block

logger = logging.getLogger(__name__)


class PlannerMessageProcessor(BaseMessageProcessor):
    """计划器消息处理器"""

    def get_agent_name(self) -> str:
        return "planner"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理计划器消息"""
        try:
            finished_messages = [message for message in messages]
            all_thoughts = []
            all_plan_steps = []

            for i, message in enumerate(finished_messages):
                if self._is_silent_node_message(message.tags):
                    continue
                if message.content.strip():
                    thought, plan_steps = self._parse_planner_content(message.content)
                    if thought.strip():
                        all_thoughts.append(thought.strip())
                    if plan_steps:
                        all_plan_steps.extend(plan_steps)
                else:
                    logger.warning(f"Planner message {i} has empty content")

            # 构建最终结果
            result = {}
            if all_thoughts:
                final_thought = "\n".join(all_thoughts)
                result["thought"] = final_thought
            if all_plan_steps:
                result["plan_steps"] = all_plan_steps
            if result:
                return result
            else:
                logger.warning("No valid planner content found, returning empty result")
                return {}
        except Exception as e:
            logger.error(f"Error processing planner messages: {e}", exc_info=True)
            return {}

    def _is_silent_node_message(self, tags: list[str]) -> bool:
        """检查是否是 silent 节点的消息（应该忽略）"""
        # 如果内容主要是这些关键词组成，且没有结构化的计划，可能是 silent 节点
        if "silent_llm_call" in tags:
            return True

        return False

    def _parse_planner_content(self, content: str) -> tuple:
        """解析计划器内容，支持完整JSON和部分内容解析"""
        # 首先尝试完整JSON解析
        try:
            plan_data = json.loads(content)
            thought = plan_data.get("thought", "")
            steps = plan_data.get("steps", [])
            return thought, steps
        except json.JSONDecodeError:
            # JSON解析失败，尝试部分解析
            return self._parse_partial_planner_content(content)
        except Exception as e:
            logger.warning(f"Failed to parse planner JSON - Other error: {e}")
            return self._parse_partial_planner_content(content)

    def _parse_partial_planner_content(self, content: str) -> tuple:
        """解析部分计划器内容，基于planner.md格式提取thought和steps"""
        thought = ""
        steps = []

        try:
            # 提取thought字段 - 支持多行内容
            thought_match = re.search(r'"thought"\s*:\s*"([^"]*(?:\\.[^"]*)*)"', content, re.DOTALL)
            if thought_match:
                thought = thought_match.group(1).replace('\\"', '"').strip()

            # 提取steps数组 - 查找完整的Step对象
            steps_match = re.search(r'"steps"\s*:\s*\[(.*)', content, re.DOTALL)
            if steps_match:
                steps_content = steps_match.group(1)
                steps = self._extract_steps_from_content(steps_content)

            logger.info(f"Partial parsing extracted - thought: '{thought[:50]}...', steps: {len(steps)} items")
            return thought, steps

        except Exception as e:
            logger.warning(f"Failed to parse partial planner content: {e}")
            return "", []

    def _extract_steps_from_content(self, steps_content: str) -> list:
        """从steps内容中提取步骤列表，基于planner.md中的Step接口格式"""
        steps = []
        try:
            if not steps_content.strip():
                return steps

            # 查找完整的Step对象 - 支持嵌套括号
            brace_count = 0
            start_pos = -1
            i = 0

            while i < len(steps_content):
                char = steps_content[i]
                if char == '{':
                    if brace_count == 0:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos != -1:
                        # 找到完整的对象
                        step_obj = steps_content[start_pos:i+1]
                        try:
                            step = json.loads(step_obj)
                            # 验证是否包含Step接口的必要字段
                            if 'title' in step and 'description' in step:
                                steps.append(step)
                        except json.JSONDecodeError:
                            # 尝试提取关键字段
                            title_match = re.search(r'"title"\s*:\s*"([^"]*)"', step_obj)
                            desc_match = re.search(r'"description"\s*:\s*"([^"]*)"', step_obj)
                            if title_match and desc_match:
                                steps.append({
                                    "title": title_match.group(1),
                                    "description": desc_match.group(1)
                                })
                        start_pos = -1
                i += 1

        except Exception as e:
            logger.warning(f"Failed to extract steps from content: {e}")

        return steps