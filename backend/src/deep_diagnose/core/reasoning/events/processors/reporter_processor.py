"""
报告生成器消息处理器

处理报告生成器Agent的消息，重命名自 ReporterEventProcessor
"""

import logging
import time
from typing import Dict, Any, List

from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class ReporterMessageProcessor(BaseMessageProcessor):
    # A. 简单去抖：仅当间隔超过阈值时才合并（避免高频拼接），默认200ms
    MERGE_DEBOUNCE_MS = 200
    _last_merge_ts = 0.0
    """报告生成器消息处理器"""

    def get_agent_name(self) -> str:
        return "reporter"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理报告生成器消息"""
        try:
            # Reporter 主要负责生成最终报告内容和URLs
            result_content = ""
            # A. 去抖：仅当合并间隔超过阈值，或确实有完成的消息时才进行合并
            now = time.time() * 1000
            finished_messages = [message for message in messages if message.is_finished]
            if finished_messages or (now - self._last_merge_ts) >= self.MERGE_DEBOUNCE_MS:
                for message in finished_messages:
                    if message.content.strip():
                        result_content += message.content
                self._last_merge_ts = now
                    
            result = {}
            if result_content.strip():
                result["result"] = result_content.strip()
            return result
        except Exception as e:
            logger.error(f"Error processing reporter messages: {e}", exc_info=True)
            return {}