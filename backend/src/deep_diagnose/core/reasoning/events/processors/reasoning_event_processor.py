"""
业务消息处理器

管理所有Agent消息处理器，重命名自 BusinessEventProcessor
"""

import logging
from typing import Dict, Any, List, Optional

from deep_diagnose.core.events.storage import MessageRepository
from deep_diagnose.core.reasoning.events.processors.planner_processor import PlannerMessageProcessor
from deep_diagnose.core.reasoning.events.processors.researcher_processor import ResearcherMessageProcessor
from deep_diagnose.core.reasoning.events.processors.reporter_processor import ReporterMessageProcessor

logger = logging.getLogger(__name__)


class BusinessMessageProcessor:
    """业务消息处理器 - 管理所有Agent消息处理器"""

    def __init__(self):
        self.processors = {
            "planner": PlannerMessageProcessor(),
            "researcher": ResearcherMessageProcessor(),
            "background_investigator": ResearcherMessageProcessor(),
            "coder": ResearcherMessageProcessor(),
            "reporter": ReporterMessageProcessor()
        }

    def process_business_messages(self, message_repository: MessageRepository, only_agents: Optional[List[str]] = None) -> Dict[str, Any]:
        """处理所有业务消息"""
        results = {}
        try:
            if only_agents:
                # 放宽 agent 名称匹配：'agent:*'、'*researcher*' 等映射到 researcher，避免空处理导致延迟
                resolved: List[str] = []
                for name in only_agents:
                    lname = (name or "").lower()
                    if lname in self.processors:
                        key = lname
                    elif any(k in lname for k in ["researcher", "investigator", "background", "coder", "agent"]):
                        key = "researcher"
                    elif "planner" in lname:
                        key = "planner"
                    elif "reporter" in lname:
                        key = "reporter"
                    else:
                        # 未知映射则跳过，避免全量遍历带来的开销
                        continue
                    if key not in resolved:
                        resolved.append(key)
                target_items = [(k, self.processors[k]) for k in resolved]
            else:
                target_items = list(self.processors.items())
            for agent_name, processor in target_items:
                # 兼容：如果按精确 agent 名称未命中，则尝试关键词匹配聚合
                agent_messages = message_repository.get_messages_by_agent(agent_name)
                if not agent_messages:
                    agent_messages = self._collect_messages_for_key(message_repository, agent_name)
                if agent_messages:
                    agent_result = processor.process_messages(agent_messages)
                    if agent_result:
                        results.update(agent_result)
            
            # logger.info(f"Final business messages result: {results}")
        except Exception as e:
            logger.error(f"Error processing business messages: {e}", exc_info=True)
        return results

    def _collect_messages_for_key(self, message_repository: MessageRepository, key: str) -> List[Any]:
        """当精确匹配为空时，基于关键字收集对应 agent 的消息列表。"""
        key = (key or "").lower()
        try:
            all_msgs = message_repository.get_all_messages()
            if key == "planner":
                return [m for m in all_msgs if "planner" in (m.agent or "").lower()]
            if key == "researcher":
                # 研究相关：避免过度匹配，将通用 'agent' 排除，使用更具体的关键词
                keywords = ["researcher", "investigator", "background", "coder"]
                return [m for m in all_msgs if any(k in (m.agent or "").lower() for k in keywords)]
            if key == "reporter":
                return [m for m in all_msgs if "reporter" in (m.agent or "").lower()]
        except Exception as e:
            logger.warning(f"Fallback collection for key '{key}' failed: {e}")
        return []

    def get_supported_agents(self) -> List[str]:
        """获取支持的Agent列表"""
        return list(self.processors.keys())