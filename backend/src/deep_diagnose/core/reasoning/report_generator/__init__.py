"""
报告生成器模块

简洁统一的报告生成接口，支持HTML和PDF格式。

使用示例：
    # 自动选择生成器（推荐）
    generator = create_auto_generator()
    result = generator.generate_simple_report("task_123", "# 报告内容")
    
    # 指定格式
    html_generator = create_html_generator()
    pdf_generator = create_pdf_generator()
"""

# 核心接口
from .core import DocumentGenerator, GenerationResult, DocumentFormat

# 具体实现
from .html_generator import HTMLGenerator, is_html_generation_available
from .pdf_generator import PDFGenerator, is_pdf_generation_available

# 工厂和便捷函数
from .factory import (
    ReportGeneratorFactory,
    GeneratorType,
    create_html_generator,
    create_pdf_generator,
    create_auto_generator
)

# 统一导出
__all__ = [
    # 核心接口
    'DocumentGenerator',
    'GenerationResult', 
    'DocumentFormat',
    
    # 具体实现
    'HTMLGenerator',
    'PDFGenerator',
    
    # 工厂
    'ReportGeneratorFactory',
    'GeneratorType',
    
    # 便捷函数
    'create_html_generator',
    'create_pdf_generator',
    'create_auto_generator',
    
    # 可用性检查
    'is_html_generation_available',
    'is_pdf_generation_available'
]


# 向后兼容的别名
def generate_diagnostic_report(task_id: str, agent: str, question: str, result: str, detail: str, 
                             submitted_at: str, completed_at: str = None, format_type: str = "auto") -> GenerationResult:
    """
    向后兼容的报告生成函数
    
    Args:
        task_id: 任务ID
        agent: 智能体名称
        question: 问题描述
        result: 诊断结果
        detail: 详细过程
        submitted_at: 提交时间
        completed_at: 完成时间
        format_type: 格式类型 (html/pdf/auto)
        
    Returns:
        生成结果
    """
    generator = ReportGeneratorFactory.create_generator(format_type)
    return generator.generate_full_report(
        task_id, agent, question, result, detail, submitted_at, completed_at
    )


def generate_simple_report(task_id: str, content: str, format_type: str = "auto") -> GenerationResult:
    """
    向后兼容的简单报告生成函数
    
    Args:
        task_id: 任务ID
        content: 报告内容
        format_type: 格式类型 (html/pdf/auto)
        
    Returns:
        生成结果
    """
    generator = ReportGeneratorFactory.create_generator(format_type)
    return generator.generate_simple_report(task_id, content)