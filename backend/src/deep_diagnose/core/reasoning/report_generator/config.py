"""
统一配置管理

提供HTML和PDF生成器的通用配置，避免重复代码。
"""

import os
from typing import Dict, Optional


class ReportConfig:
    """报告生成器统一配置"""
    
    def __init__(self, output_dir: str, enable_oss: bool = True):
        """
        初始化配置
        
        Args:
            output_dir: 输出目录
            enable_oss: 是否启用OSS上传
        """
        self.output_dir = output_dir
        self.enable_oss = enable_oss
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
    
    def get_colors(self) -> Dict[str, str]:
        """获取统一的配色方案"""
        return {
            # 主色调
            'primary': '#1A202C',      # 深炭灰
            'secondary': '#4A5568',    # 中性灰
            'accent': '#3182CE',       # 专业蓝
            'accent_light': '#63B3ED', # 浅蓝
            
            # 背景色
            'bg_light': '#F7FAFC',     # 极浅灰蓝
            'bg_medium': '#EDF2F7',    # 浅灰蓝
            'bg_dark': '#E2E8F0',      # 中灰蓝
            
            # 边框与线条
            'border_light': '#E2E8F0',
            'border_medium': '#CBD5E0',
            'border_accent': '#3182CE',
            
            # 特殊用途
            'code_text': '#D53F8C',    # 代码文本
            'code_bg': '#F7FAFC',      # 代码背景
            'quote_text': '#38A169',   # 引用文本
            'quote_bg': '#F0FFF4',     # 引用背景
            
            # 表格色彩
            'table_header_bg': '#4299E1',
            'table_header_text': '#FFFFFF',
            'table_border': '#CBD5E0',
            'table_stripe': '#F7FAFC',
            
            # 状态色彩
            'success': '#38A169',      # 成功
            'warning': '#D69E2E',      # 警告
            'error': '#E53E3E',        # 错误
            'info': '#3182CE',         # 信息
        }


class HTMLConfig(ReportConfig):
    """HTML生成器配置"""
    
    def get_css_styles(self) -> str:
        """获取CSS样式"""
        colors = self.get_colors()
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudBot智能体-深度诊断报告</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: {colors['primary']};
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: {colors['bg_light']};
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: {colors['accent']};
            margin-top: 2em;
            margin-bottom: 1em;
        }}
        
        h1 {{ font-size: 2.5em; border-bottom: 3px solid {colors['accent']}; padding-bottom: 10px; }}
        h2 {{ font-size: 2em; border-bottom: 2px solid {colors['accent_light']}; padding-bottom: 8px; }}
        h3 {{ font-size: 1.5em; }}
        
        p {{ margin-bottom: 1em; }}
        
        code {{
            background-color: {colors['code_bg']};
            color: {colors['code_text']};
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }}
        
        pre {{
            background-color: {colors['bg_medium']};
            border: 1px solid {colors['border_light']};
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        th, td {{
            border: 1px solid {colors['table_border']};
            padding: 12px;
            text-align: left;
        }}
        
        th {{
            background-color: {colors['table_header_bg']};
            color: {colors['table_header_text']};
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{
            background-color: {colors['table_stripe']};
        }}
        
        blockquote {{
            border-left: 4px solid {colors['accent']};
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: {colors['quote_bg']};
            color: {colors['quote_text']};
        }}
        
        ul, ol {{ margin: 1em 0; padding-left: 2em; }}
        li {{ margin-bottom: 0.5em; }}
        
        .indent {{ margin-left: 2em; }}
        
        hr {{
            border: none;
            height: 2px;
            background-color: {colors['border_medium']};
            margin: 2em 0;
        }}
    </style>
</head>
<body>
"""
    
    def get_html_footer(self) -> str:
        """获取HTML页脚"""
        return """
</body>
</html>
"""


class PDFConfig(ReportConfig):
    """PDF生成器配置"""
    
    def get_document_config(self) -> Dict:
        """获取PDF文档配置"""
        try:
            from reportlab.lib.pagesizes import A4
            return {
                'pagesize': A4,
                'topMargin': 72,
                'bottomMargin': 72,
                'leftMargin': 72,
                'rightMargin': 72
            }
        except ImportError:
            return {
                'pagesize': (595.27, 841.89),  # A4 size in points
                'topMargin': 72,
                'bottomMargin': 72,
                'leftMargin': 72,
                'rightMargin': 72
            }