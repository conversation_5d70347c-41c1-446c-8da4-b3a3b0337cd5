"""
报告生成器工厂

提供统一的生成器创建接口，自动选择可用的生成器。
"""

import logging
from typing import Optional, Union
from enum import Enum

from .core import DocumentGenerator, DocumentFormat
from .html_generator import HTMLGenerator, is_html_generation_available
from .pdf_generator import PDFGenerator, is_pdf_generation_available

logger = logging.getLogger(__name__)


class GeneratorType(Enum):
    """生成器类型"""
    HTML = "html"
    PDF = "pdf"
    AUTO = "auto"  # 自动选择


class ReportGeneratorFactory:
    """
    报告生成器工厂
    
    功能：
    1. 创建HTML或PDF生成器
    2. 自动选择可用的生成器
    3. 检查生成器可用性
    """
    
    @staticmethod
    def create_generator(
        generator_type: Union[GeneratorType, str] = GeneratorType.AUTO,
        output_dir: Optional[str] = None,
        enable_oss: bool = True
    ) -> DocumentGenerator:
        """
        创建报告生成器
        
        Args:
            generator_type: 生成器类型 (html/pdf/auto)
            output_dir: 输出目录
            enable_oss: 是否启用OSS上传
            
        Returns:
            生成器实例
            
        Raises:
            ImportError: 当所需依赖不可用时
            ValueError: 当生成器类型无效时
        """
        # 处理字符串类型
        if isinstance(generator_type, str):
            try:
                generator_type = GeneratorType(generator_type.lower())
            except ValueError:
                raise ValueError(f"Invalid generator type: {generator_type}. Use 'html', 'pdf', or 'auto'")
        
        # 自动选择
        if generator_type == GeneratorType.AUTO:
            if is_pdf_generation_available():
                generator_type = GeneratorType.PDF
            elif is_html_generation_available():
                generator_type = GeneratorType.HTML
            else:
                raise ImportError("No report generator is available")
        
        # 创建具体生成器
        if generator_type == GeneratorType.PDF:
            if not is_pdf_generation_available():
                raise ImportError("PDF generation requires reportlab. Install with: pip install reportlab")
            return PDFGenerator(output_dir, enable_oss)
        
        elif generator_type == GeneratorType.HTML:
            if not is_html_generation_available():
                raise ImportError("HTML generation is not available")
            return HTMLGenerator(output_dir, enable_oss)
        
        else:
            raise ValueError(f"Unsupported generator type: {generator_type}")
    
    @staticmethod
    def get_available_generators() -> list[GeneratorType]:
        """获取可用的生成器类型列表"""
        available = []
        
        if is_html_generation_available():
            available.append(GeneratorType.HTML)
        
        if is_pdf_generation_available():
            available.append(GeneratorType.PDF)
        
        return available
    
    @staticmethod
    def is_generator_available(generator_type: Union[GeneratorType, str]) -> bool:
        """检查指定生成器是否可用"""
        if isinstance(generator_type, str):
            try:
                generator_type = GeneratorType(generator_type.lower())
            except ValueError:
                return False
        
        if generator_type == GeneratorType.HTML:
            return is_html_generation_available()
        elif generator_type == GeneratorType.PDF:
            return is_pdf_generation_available()
        elif generator_type == GeneratorType.AUTO:
            return is_html_generation_available() or is_pdf_generation_available()
        
        return False


# 便捷函数
def create_html_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> DocumentGenerator:
    """创建HTML生成器"""
    return ReportGeneratorFactory.create_generator(GeneratorType.HTML, output_dir, enable_oss)


def create_pdf_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> DocumentGenerator:
    """创建PDF生成器"""
    return ReportGeneratorFactory.create_generator(GeneratorType.PDF, output_dir, enable_oss)


def create_auto_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> DocumentGenerator:
    """创建自动选择的生成器（优先PDF）"""
    return ReportGeneratorFactory.create_generator(GeneratorType.AUTO, output_dir, enable_oss)