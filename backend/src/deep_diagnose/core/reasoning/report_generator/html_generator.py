"""
HTML生成器

简洁的HTML报告生成器，职责单一，接口清晰。
"""

import logging
import os
import tempfile
from datetime import datetime
from typing import Optional

from .core import DocumentGenerator, GenerationResult, DocumentFormat
from .config import HTMLConfig
from .processors import HTMLContentProcessor
from .storage import StorageManager

logger = logging.getLogger(__name__)


class HTMLGenerator(DocumentGenerator):
    """
    HTML报告生成器
    
    功能：
    1. 将Markdown内容转换为HTML报告
    2. 支持本地保存和OSS上传
    3. 自动清理旧文件
    """
    
    def __init__(self, output_dir: Optional[str] = None, enable_oss: bool = True):
        """
        初始化HTML生成器
        
        Args:
            output_dir: 输出目录，如果为None则使用临时目录
            enable_oss: 是否启用OSS上传
        """
        self.output_dir = output_dir or tempfile.gettempdir()
        self.enable_oss = enable_oss
        
        # 初始化组件
        self.config = HTMLConfig(self.output_dir, enable_oss)
        self.processor = HTMLContentProcessor()
        self.storage = StorageManager(self.output_dir, enable_oss, DocumentFormat.HTML)
        
        logger.info(f"HTML Generator initialized: {self.output_dir}, OSS: {enable_oss}")
    
    @property
    def format(self) -> DocumentFormat:
        """获取文档格式"""
        return DocumentFormat.HTML
    
    def generate_full_report(
        self, 
        task_id: str, 
        agent: str, 
        question: str,
        result: str, 
        detail: str,
        submitted_at: str, 
        completed_at: Optional[str] = None
    ) -> GenerationResult:
        """
        生成完整的诊断报告
        
        Args:
            task_id: 任务ID
            agent: 智能体名称
            question: 问题描述
            result: 诊断结果
            detail: 详细过程
            submitted_at: 提交时间
            completed_at: 完成时间
            
        Returns:
            生成结果
        """
        try:
            # 构建完整报告内容
            content = self._build_full_report_content(
                task_id, agent, question, result, detail, submitted_at, completed_at
            )
            
            # 生成HTML文档
            return self._generate_html_document(task_id, content)
            
        except Exception as e:
            error_msg = f"Failed to generate full HTML report for task {task_id}: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )
    
    def generate_simple_report(self, task_id: str, content: str) -> GenerationResult:
        """
        生成简单诊断报告
        
        Args:
            task_id: 任务ID
            content: 报告内容（Markdown格式）
            
        Returns:
            生成结果
        """
        try:
            return self._generate_html_document(task_id, content)
            
        except Exception as e:
            error_msg = f"Failed to generate simple HTML report for task {task_id}: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> None:
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        self.storage.cleanup_old_files(max_age_hours)
    
    def _build_full_report_content(
        self, 
        task_id: str, 
        agent: str, 
        question: str,
        result: str, 
        detail: str,
        submitted_at: str, 
        completed_at: Optional[str] = None
    ) -> str:
        """构建完整报告的Markdown内容"""
        completed_time = completed_at or datetime.now().isoformat()
        
        return f"""# CloudBot智能体深度诊断报告

## 问题描述

{question}

## 诊断结果

{result}

## 详细过程

{detail}

---

*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
    
    def _generate_html_document(self, task_id: str, content: str) -> GenerationResult:
        """生成HTML文档"""
        try:
            # 生成文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"diagnostic_report_{task_id}_{timestamp}.html"
            file_path = os.path.join(self.output_dir, filename)
            
            # 处理Markdown内容
            html_body = self.processor.process_content(content)
            
            # 构建完整HTML
            html_content = self.config.get_css_styles() + html_body + self.config.get_html_footer()
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML document generated: {file_path}")
            
            # 保存文件（包含OSS上传）
            return self.storage.save_file(file_path, task_id)
            
        except Exception as e:
            error_msg = f"Failed to generate HTML document: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )


def is_html_generation_available() -> bool:
    """检查HTML生成功能是否可用"""
    return True  # HTML生成不需要额外依赖


def create_html_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> HTMLGenerator:
    """创建HTML生成器实例"""
    return HTMLGenerator(output_dir, enable_oss)
