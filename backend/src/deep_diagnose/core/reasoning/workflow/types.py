

import operator
from typing import Annotated

from langgraph.graph import MessagesState

from deep_diagnose.prompts.planner_model import Plan


class ReasoningState(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "en-US"
    observations: list[str] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    mcp_servers_description: str = ""
    sop_name: str = ""


# Backward compatibility: keep State as an alias for ReasoningState
State = ReasoningState
