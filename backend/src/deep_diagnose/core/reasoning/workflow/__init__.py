def __getattr__(name):
    """Lazy import to avoid circular dependencies"""
    if name == 'build_graph_with_memory':
        from .builder import build_graph_with_memory
        return build_graph_with_memory
    elif name == 'build_graph':
        from .builder import build_graph
        return build_graph
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    "build_graph_with_memory", 
    "build_graph",
]