import asyncio
import json
import logging
import time
from typing import Dict

from langchain_core.tools.base import BaseTool

from deep_diagnose.common.utils import time_utils
from deep_diagnose.common.utils.json_utils import extract_items_from_incomplete_json, extract_json_code_block, \
    repair_json_output
from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.common.utils.task_scheduler import TaskScheduler, Task
from deep_diagnose.core.interactive.param_validator import validate_params
from deep_diagnose.core.interactive.state import ExecutionState, StepState
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum
from deep_diagnose.tools.decorators import catch_all_async_errors

logger = logging.getLogger(__name__)


@catch_all_async_errors
async def plan_and_execute(execution_state: ExecutionState):
    # 准备planner的参数
    biz_params = {
        "currentTime": time_utils.current_time_with_week(),
        "tools": _load_tool_summary(execution_state.tool_map)
    }
    # 启动调度器
    scheduler = TaskScheduler()
    scheduler_task = asyncio.create_task(scheduler.aschedule(execution_state.task_queue))
    logger.info("Task scheduler is created.")
    # 异步调用规划器，按步骤流水执行
    responses = app_bailian.app_stream_call(app=BailianAppEnum.planner, prompt=execution_state.question,
                                            biz_params=biz_params)
    planner_start_ts = time.time()
    async for result, _ in responses:
        # 优雅退出
        if execution_state.fast_path_scenario is not None:
            logger.info("Planner exit due to fast path.")
            return
        # 跳过空输出
        if is_blank(result):
            continue
        # 将输出内容追加到字符串中
        text = execution_state.plan_content + result
        # 分析输出内容，获取未完成的 JSON 对象
        partial_list, size = extract_items_from_incomplete_json(text[execution_state.analyzed_chars_of_plan + 1:])
        # 将规划的步骤加入执行队列
        for step in partial_list:
            if step['id'] not in execution_state.analyzed_steps:
                task = Task(
                    idx=int(step['id']),
                    name=step.get('tool', ''),
                    func=_step_execute,  # 固定调用该方法，具体的工具执行在方法内部决定
                    args=[step, execution_state],  # 默认传入该步骤的规划和整体的状态
                    dependencies=[int(i) for i in step['dependence']],
                )
                await execution_state.task_queue.put(task)
                execution_state.analyzed_steps.add(step['id'])
                logger.info(f"Step {step.get('id')} with tool {step.get('tool')} is put to task queue.")
        # 维护状态，更新已处理的字符数和输出内容
        execution_state.analyzed_chars_of_plan += size
        execution_state.plan_content = text
    planner_stop_ts = time.time()
    logger.info(f"Call of planner costs {planner_stop_ts - planner_start_ts:.3f} s.")
    # 注入结束任务，等待调度器停止
    await execution_state.task_queue.put(None)
    await scheduler_task
    logger.info("Task scheduler is stopped.")


@catch_all_async_errors
async def _step_execute(step: dict, state: ExecutionState) -> None:
    """
    异步执行单个步骤的核心逻辑，包括参数提取、验证及工具调用。

    参数:
        step (dict): 当前步骤的定义，包含步骤ID、工具名称、输入内容、分析结果、依赖步骤等。
        state (ExecutionState): 当前任务的执行状态，包含步骤状态、工具映射、任务队列等信息。

    返回值:
        None: 该函数没有返回值，执行结果通过 state.step_states 中的 StepState 对象记录。
    """
    logger.info(f"Executing step: {step}")

    # 快速路径优雅退出
    if state.fast_path_scenario is not None:
        logger.info(f"Executor of step {step["id"]} exit due to fast path.")
        return
    # 创建步骤状态对象并注册到执行状态中
    step_state = StepState(step)
    step_state.status = "Executing"
    state.step_states[step_state.id] = step_state

    # 跳过空的工具（代表纯思考步骤）
    if is_blank(step_state.tool):
        step_state.tool = None
        step_state.status = "Success"
        return

    # 跳过不存在的工具
    if step_state.tool not in state.tool_map:
        logger.error(f"{step_state.tool} tool is not found.")
        step_state.error = "指定的工具不存在"
        step_state.status = "Fail"
        return

    # 准备工具和参数提取器的业务参数
    tool = state.tool_map[step_state.tool]
    biz_params = {
        "tool": step_state.tool,
        "input": step_state.input,
        "analysis": step_state.analysis,
        "dependence": _collect_dependency_results(state, step),
        "tool_doc": _load_tool_input_description(tool),
    }

    # 调用参数提取器获取生成的参数并修复可能的错误
    param_content = await app_bailian.app_call(app=BailianAppEnum.param_extractor, prompt="ecs-deep-diagnose",
                                               biz_params=biz_params)
    step_state.input_json = repair_json_output(extract_json_code_block(param_content))
    input_params = json.loads(step_state.input_json)
    # 快速路径优雅退出
    if state.fast_path_scenario is not None:
        logger.info(f"Executor of step {step["id"]} exit due to fast path.")
        return
    # 校验生成的参数，防止幻觉
    validation_context = biz_params['input'] + biz_params['analysis'] + biz_params['dependence']
    validation_error = validate_params(input_params, validation_context)
    if validation_error is not None:
        step_state.error = "生成的工具参数未通过校验：" + validation_error
        step_state.status = "Fail"
        logger.error(f"Param validation failed. {validation_error}")
        return
    logger.info(f"Invoking tool {step_state.tool} with params {input_params}.")
    # 执行方法
    try:
        step_state.result = await tool.ainvoke(input_params)
        step_state.result = "None" if not step_state.result else step_state.result
        step_state.status = "Success"
        logger.info(f"Step {step_state.id} success. Input: {step_state.input_json} Output: {step_state.result}")
    except Exception as e:
        logger.error(f"Error when executing tool {step_state.tool}: {e}", exc_info=True)
        step_state.error = "工具调用时报错，错误信息：" + str(e)
        step_state.status = "Fail"
        return


def _collect_dependency_results(state: ExecutionState, step: dict) -> str:
    """
    收集当前步骤所依赖的其他步骤的执行结果。

    参数:
        state (ExecutionState): 当前任务的执行状态对象，包含所有步骤的执行结果。
        step (dict): 当前步骤的定义，其中可能包含依赖的其他步骤ID列表。

    返回:
        str: 所有依赖步骤的执行结果拼接成的字符串，每条结果之间用换行符分隔。
           如果当前步骤没有依赖或所有依赖都不存在，返回'无'。
    """
    dependencies = step.get('dependence', [])
    if not dependencies:
        return '无'
    result = []
    for key in dependencies:
        # 检查键是否存在，避免KeyError
        if key in state.step_states:
            result.append(str(state.step_states[key].result))
        else:
            # 如果依赖的键不存在，记录警告但继续执行
            logger.warning(f"Dependency step {key} not found in step_states.")
    return '\n'.join(result)


def _get_first_non_empty_line(text):
    lines = text.splitlines()
    for line in lines:
        if line.strip():
            return line
    return None


def _load_tool_input_description(tool: BaseTool) -> str:
    """
    从工具描述中提取输入参数说明部分，即"Returns:"之前的内容。

    参数:
        tool (BaseTool): 要提取描述的工具对象，其 description 属性包含完整的工具说明文本。

    返回值:
        str: 提取后的输入参数说明文本，只包含"Returns:"之前的内容。当未找到"Returns:"关键字时，返回全部文档
    """
    lines = tool.description.splitlines()  # 将文本按行分割
    result = []
    for line in lines:
        if "Returns:" in line:
            break  # 遇到关键字所在行就停止
        result.append(line)  # 收集关键字之前的行
    return '\n'.join(result)  # 返回拼接后的结果


def _extract_before_keyword(text, keyword):
    """
    从多行文本中提取关键字之前的所有内容。

    :param text: 多行文本
    :param keyword: 要查找的关键字
    :return: 关键字之前的所有内容，如果未找到关键字则返回空字符串
    """
    lines = text.splitlines()  # 将文本按行分割
    result = []

    for line in lines:
        if keyword in line:
            break  # 遇到关键字所在行就停止
        result.append(line)  # 收集关键字之前的行

    return '\n'.join(result)  # 返回拼接后的结果


def _load_tool_summary(tool_map: Dict[str, BaseTool]) -> str:
    summaries = []
    for tool in tool_map.values():
        if tool != "listKnowledge":
            summaries.append(f"- {tool.name}: {_get_first_non_empty_line(tool.description)}")
    return str.join("\n", summaries)
