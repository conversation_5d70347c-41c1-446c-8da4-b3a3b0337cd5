import asyncio
import logging
from typing import Optional

from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.common.utils.task_scheduler import Task

logger = logging.getLogger(__name__)


class ExecutionState:
    def __init__(self):
        # 控制选项
        self.slow_path_enable = True
        self.fast_path_enable = True
        self.fast_path_mode = 'default'
        # 通用
        self.question = ''
        self.tool_map = {}
        self.finished = False
        # 慢速路径规划
        self.plan_content = ''
        self.analyzed_chars_of_plan = 0
        self.analyzed_steps = set()
        self.task_queue = asyncio.Queue[Optional[Task]]()
        # 快速路径规划
        self.fast_path_match_completed = False
        self.fast_path_scenario = None
        # 执行
        self.step_states = dict[str, StepState]()
        # 总结
        self.conclusion_started = False
        self.reason_content = ''
        self.result_content = ''

    def to_display_str(self) -> str:
        if self.fast_path_mode == "instruction":
            return self._to_display_str_instruction()
        else:
            return self._to_display_str_default()

    def _to_display_str_default(self) -> str:
        """
        将 ExecutionState 对象转换为字符串格式，用于展示诊断执行的步骤信息、思考内容和结果回复。
        
        返回:
            str: 包含所有步骤状态信息、思考内容和结果回复的字符串表示。
            
        说明:
            - 如果没有步骤状态，则返回“正在生成解决方案”。
            - 按照 step_id 排序后输出每个 StepState 的展示字符串。
            - 如果存在 reason_content，则追加“### 思考”部分。
            - 如果存在 result_content，则追加“### 回复”部分。
        """
        contents = []
        try:
            if not self.fast_path_match_completed:
                return "正在尝试匹配快速路径"
            elif self.fast_path_scenario is None and len(self.step_states) == 0 and not self.conclusion_started:
                return "未命中快速路径，正在生成解决方案"

            # 遍历按 step_id 排序后的 step_states，生成每个步骤的展示字符串
            for step_id, step_state in sorted(self.step_states.items()):
                contents.append(step_state.to_display_str())
            # 添加思考内容（如果存在）
            if not is_blank(self.reason_content):
                contents.append("## 思考")
                contents.append(self.reason_content)
            # 添加回复内容（如果存在）
            if not is_blank(self.result_content):
                contents.append("## 回复")
                contents.append(self.result_content)
        except Exception as e:
            logger.error(f"Generate state display content error: {e}", exc_info=True)

        # 使用换行符连接所有内容并返回
        return str.join("\n\n", contents)

    def _to_display_str_instruction(self) -> str:
        return self.result_content if not is_blank(self.result_content) else ""


class StepState:

    def __init__(self, step: dict):
        self.tool = step.get('tool', None)
        self.id = step.get('id', None)
        self.input = step.get('input', None)
        self.analysis = step.get('analysis', None)
        self.input_json = None
        self.result = None
        self.error = None
        self.status = "Wait"

    def to_display_str(self) -> str:
        """
        将当前步骤状态转换为HTML格式的字符串表示，用于展示。
        
        返回:
            str: 包含步骤信息的HTML格式字符串，包括ID、工具名称、状态、输入和输出。
        """
        # 设置对应的工具名称
        tool = "Thinking" if is_blank(self.tool) else self.tool

        # 生成HTML内容
        contents = [f'<details>\n<summary>({self.id}) {tool} [{self.status}]</summary>']
        if self.input_json:
            contents.append('### Input')
            contents.append(f'{self.input_json}')
        if self.result:
            contents.append('### Output')
            contents.append(f'{self.result}')
        if self.error:
            contents.append('### Error')
            contents.append(f'{self.error}')
        contents.append('</details>')
        return str.join("\n\n", contents)
