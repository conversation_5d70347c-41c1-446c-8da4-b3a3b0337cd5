import asyncio
import json
import logging
import os
import re
from typing import Optional

from deep_diagnose.common.utils.json_utils import extract_json_code_block, repair_json_output
from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.common.utils.task_scheduler import Task, TaskScheduler
from deep_diagnose.common.utils.time_utils import current_time_with_week, offset_timestamp
from deep_diagnose.core.interactive.param_validator import validate_params
from deep_diagnose.core.interactive.state import ExecutionState, StepState
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum
from deep_diagnose.tools.decorators import catch_all_async_errors

logger = logging.getLogger(__name__)


@catch_all_async_errors
async def plan_and_execute(execution_state: ExecutionState) -> None:
    """
    异步函数，用于规划和执行快速路径任务。

    该函数首先尝试匹配快速路径场景，如果匹配成功则加载对应的执行计划，
    清理慢速路径占用的资源，并将计划中的步骤构造成任务放入队列，
    最后启动任务调度器按顺序执行这些任务。

    参数:
        execution_state (ExecutionState): 执行状态对象，包含当前执行的上下文信息

    返回值:
        None
    """

    # 匹配快速路径场景
    scenario_var = await _match_fast_path(execution_state)

    # 加载快速路径执行计划
    plan = _load_fast_path_plan(None if scenario_var is None else scenario_var.get("scenario", None))
    if plan is None:
        # 未命中快速路径，或加载计划出错，直接退出
        logger.info("No fast path scenario matched.")
        return

    # 校验快速路径参数
    validation_error = validate_params(scenario_var, execution_state.question)
    if validation_error is not None:
        logger.error(f"Fast path validation failed. {validation_error}")
        return

    # 清理慢速路径占用的资源
    execution_state.fast_path_scenario = scenario_var.pop("scenario", None)
    execution_state.step_states.clear()
    await execution_state.task_queue.put(None)
    logger.info(f"{execution_state.fast_path_scenario} is matched in fast path.")

    # 部分工具可能不可用，修正执行计划
    plan = _prune_invalid_steps(plan, execution_state.tool_map)

    # 创建新的任务队列，并根据计划构建任务
    task_queue = asyncio.Queue[Optional[Task]]()
    for step in plan:
        # 构造任务对象
        task = Task(
            idx=int(step['id']),
            name=step.get('tool', ''),
            func=_step_execute,  # 固定调用该方法，具体的工具执行在方法内部决定
            args=[step, execution_state, scenario_var],  # 默认传入该步骤的规划、整体的状态和场景变量
            dependencies=[int(i) for i in step['dependence']],
        )
        # 初始化步骤状态并加入执行状态中
        step_state = StepState(step)
        execution_state.step_states[step['id']] = step_state
        await task_queue.put(task)

    # 添加结束标记
    await task_queue.put(None)
    logger.info(f"{len(plan)} tasks are put to task queue.")

    # 启动任务调度器，按顺序执行任务
    logger.info("Task scheduler is started.")
    scheduler = TaskScheduler()
    scheduler_task = asyncio.create_task(scheduler.aschedule(task_queue))
    await scheduler_task
    logger.info("Task scheduler is stopped.")
    return


@catch_all_async_errors
async def _step_execute(step: dict, execution_state: ExecutionState, scenario_dict: dict) -> None:
    """
    异步执行单个步骤任务。

    该函数负责执行快速路径中的单个步骤，包括变量注入、工具调用和状态更新。
    它会根据依赖步骤的结果和场景变量来构造当前步骤的输入参数，
    然后调用相应的工具执行具体操作，并更新步骤执行状态。

    参数:
        step (dict): 当前步骤的配置信息，包含id、工具名称、输入模板、参数模板和依赖关系等
        execution_state (ExecutionState): 整体执行状态对象，包含所有步骤状态和工具映射
        scenario_dict (dict): 场景相关的变量字典，用于参数注入

    返回值:
        None
    """
    logger.info(f"Step {step['id']} is executing.")

    # 更新步骤状态为执行中
    step_state = execution_state.step_states[step['id']]
    step_state.status = "Executing"

    # 跳过空的工具（代表纯思考步骤）
    if is_blank(step_state.tool):
        step_state.tool = None
        step_state.status = "Success"
        return

    # 准备注入的变量，包括依赖步骤的输出结果、原始问题和场景变量
    var_dict = {"output": [json.loads(execution_state.step_states[i].result) for i in step['dependence']],
                "question": execution_state.question}
    var_dict.update(scenario_dict)

    # 通过变量注入生成实际的输入和参数
    step_state.input = _inject(step['input'], var_dict)
    input_params = _inject(step['fastParams'], var_dict)
    step_state.input_json = json.dumps(input_params)

    # 执行具体的工具调用
    try:
        tool = execution_state.tool_map[step['tool']]
        step_state.result = await tool.ainvoke(input_params)
        step_state.result = "None" if not step_state.result else step_state.result
        step_state.status = "Success"
        logger.info(f"Step {step_state.id} success. Input: {step_state.input_json} Output: {step_state.result}")
    except Exception as e:
        logger.error(f"Error when executing tool {step_state.tool}: {e}", exc_info=True)
        step_state.error = "工具调用时报错，错误信息：" + str(e)
        step_state.status = "Fail"
        return


def _inject(raw, var_dict: dict):
    """
    递归地将变量字典中的值注入到原始数据结构中。

    该函数支持字符串、列表和字典类型的递归处理。对于字符串类型，
    它会调用 _inject_str 进行占位符替换；对于列表和字典，它会递归
    处理每个元素或键值对；对于其他类型，则直接返回原值。

    参数:
        raw (any): 原始数据，可以是字符串、列表、字典或其他类型
        var_dict (dict): 包含要注入的变量的字典

    返回值:
        any: 注入变量后的新数据结构，类型与输入相同
    """
    if isinstance(raw, str):
        # 字符串类型，调用专门的字符串注入函数
        return _inject_str(raw, var_dict)
    elif isinstance(raw, list):
        # 列表类型，递归处理每个元素
        return [_inject(i, var_dict) for i in raw]
    elif isinstance(raw, dict):
        # 字典类型，递归处理每个键值对
        return {k: _inject(v, var_dict) for k, v in raw.items()}
    else:
        # 其他类型，直接返回原值
        return raw


def _inject_str(raw: str, var_dict: dict) -> str:
    """
    将变量字典中的值注入到字符串中的占位符位置。

    该函数使用正则表达式匹配字符串中形如 ${xxx} 的占位符，
    并尝试从变量字典中获取对应的值进行替换。如果替换过程中
    发生异常，则记录错误日志并保留原始占位符。

    参数:
        raw (str): 包含占位符的原始字符串
        var_dict (dict): 包含要注入的变量的字典

    返回值:
        str: 替换占位符后的新字符串
    """

    def replacer(match):
        # 提取占位符内容并尝试从变量字典中获取值
        placeholder = match.group(1)
        try:
            # 使用eval执行占位符表达式，允许访问var_dict中的变量
            return eval(placeholder, {"offset_timestamp": offset_timestamp}, var_dict)
        except Exception as e:
            # 替换失败时记录错误并返回原始占位符
            logger.error(f"Error when injecting {placeholder}: {e}")
            return placeholder

    # 匹配${xxx}格式的占位符
    pattern = r'\$\{([^}]+)\}'
    # 使用replacer函数替换所有匹配的占位符
    return re.sub(pattern, replacer, raw)


async def _match_fast_path(execution_state: ExecutionState) -> dict:
    """
    根据执行状态中的快速路径模式匹配对应的场景。

    该函数根据`execution_state.fast_path_mode`的值来决定使用哪种方式匹配快速路径场景。
    如果模式为'instruction'，则使用指令方式匹配；否则使用默认方式匹配。

    参数:
        execution_state (ExecutionState): 执行状态对象，包含快速路径模式和用户问题等信息

    返回值:
        dict: 包含匹配结果的变量字典
    """
    match execution_state.fast_path_mode:
        case 'instruction':
            # 使用指令方式匹配快速路径
            var_dict = _match_fast_path_instruction(execution_state)
        case _:
            # 使用默认方式匹配快速路径
            var_dict = await _match_fast_path_default(execution_state)

    # 标记快速路径场景匹配已完成
    execution_state.fast_path_match_completed = True
    return var_dict


def _match_fast_path_instruction(execution_state: ExecutionState) -> dict:
    """
    通过指令方式匹配快速路径场景。

    该函数将用户问题按逗号分割，第一部分作为场景名称，后续部分作为参数。
    构造一个包含场景名称和参数的字典并返回。

    参数:
        execution_state (ExecutionState): 执行状态对象，包含用户问题

    返回值:
        dict: 包含场景名称和参数的字典
    """
    # 按逗号分割用户问题
    question_parts = execution_state.question.split(",")

    # 将指令改写为可读的问题
    question_map = {
        "FASTPATH_NC_DOWN@BAILIAN": f"{question_parts[1]}在{question_parts[2]}发生了宕机，请排查宕机原因，确认宿主机当前是否已经恢复",
        "FASTPATH_VM_DOWN@BAILIAN": f"{question_parts[1]}在{question_parts[2]}发生了宕机，请排查宕机原因，确认实例当前是否已经恢复，并给出处理建议"
    }
    execution_state.question = question_map[question_parts[0]]

    # 构造变量字典，第一部分作为场景名称，将后续部分作为参数添加到字典中
    var_dict = {
        "scenario": question_parts[0].strip() if question_parts else ""
    }
    for i in range(1, len(question_parts)):
        key = "arg" + str(i)
        var_dict[key] = question_parts[i].strip()

    return var_dict


async def _match_fast_path_default(execution_state: ExecutionState) -> dict:
    """
    异步函数，用于匹配快速路径场景，默认模式。

    该函数通过调用大语言模型（LLM）来识别当前问题是否符合预定义的快速路径场景。
    它会加载场景定义文件，构建业务参数，并调用百炼应用进行场景匹配。
    匹配成功后，将结果记录到执行状态中并返回解析后的变量字典。

    参数:
        execution_state (ExecutionState): 执行状态对象，包含用户问题等上下文信息

    返回值:
        dict: 包含匹配结果的变量字典，通常包含'scenario'键表示匹配到的场景名称
    """
    # 构建业务参数，包含所有可用的快速路径场景定义
    biz_params = {
        "user_prompt_params": {
            "scenarios": _load_scenario_file(),
            "current_time": current_time_with_week()
        }
    }

    # 调用百炼应用服务进行场景匹配
    content = await app_bailian.app_call(app=BailianAppEnum.scenario_matcher, prompt=execution_state.question,
                                         biz_params=biz_params)

    # 提取并修复LLM返回的JSON格式内容
    content = repair_json_output(extract_json_code_block(content))

    # 解析JSON内容为变量字典并返回
    try:
        return json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON content: {e}", exc_info=True)
        return {}


def _read_json_file(file_path):
    """
    读取指定路径的JSON文件并解析其内容。

    Args:
        file_path (str): JSON文件的路径。

    Returns:
        list: 解析后的JSON数据，如果读取或解析失败则返回None。
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)  # 解析JSON文件内容
            return data
    except Exception as e:
        logger.error(f"Load {file_path} failed: {e}", exc_info=True)
        return None


def _load_fast_path_plan(scenario_name: str | None):
    """
    根据场景名称加载对应的快速路径计划。

    Args:
        scenario_name (str): 场景名称，用于构建计划文件路径。

    Returns:
        list: 加载并解析后的计划数据，如果场景名称为空或文件不存在则返回None。
    """
    if scenario_name is None:
        return None

    current_path = os.path.dirname(__file__)
    file_path = os.path.join(current_path, "..", "..", "prompts", "fast_path_plan", f"{scenario_name}.json")
    file_path = os.path.abspath(file_path)
    return _read_json_file(file_path)


def _load_scenario_file() -> str | None:
    current_path = os.path.dirname(__file__)
    file_path = os.path.join(current_path, "..", "..", "prompts", "fast_path_plan", "scenarios.md")
    file_path = os.path.abspath(file_path)
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            # 将文件读取为字符串
            data = file.read()
            return data
    except Exception as e:
        logger.error(f"Load {file_path} failed: {e}", exc_info=True)
        return None


def _prune_invalid_steps(steps: list, tool_map: dict) -> list:
    """
    过滤工具不可用的步骤及依赖缺失的步骤，并对剩余步骤重新编号。

    Args:
        steps (list): 输入的步骤列表，每个元素为包含`ID`和`dependence`字段的字典。
        tool_map (dict): 工具映射表，用于检查步骤所需的工具是否可用。

    Returns:
        list: 处理后的步骤列表，包含重新编号的ID和更新后的dependence。
    """
    # 1. 删除工具不存在于工具表的步骤（可能因为权限导致工具不开放）
    filtered_steps = [step for step in steps if is_blank(step['tool']) or step['tool'] in tool_map]

    # 2. 循环删除依赖缺失的步骤
    prev_count = -1
    while len(filtered_steps) != prev_count:
        prev_count = len(filtered_steps)
        valid_ids = {step['id'] for step in filtered_steps}
        filtered_steps = [
            step for step in filtered_steps
            if all(dep_id in valid_ids for dep_id in step.get('dependence', []))
        ]

    # 3. 构建旧ID到新ID的映射
    old_to_new_id = {step['id']: str(i + 1) for i, step in enumerate(filtered_steps)}

    # 4. 重新生成步骤列表并更新dependence
    new_steps = []
    for step in filtered_steps:
        new_step = step.copy()  # 保留原字典其他字段
        new_step['id'] = old_to_new_id[step['id']]
        new_step['dependence'] = [old_to_new_id[dep_id] for dep_id in step.get('dependence', [])]
        new_steps.append(new_step)

    # 5. 当步骤列表为空时，添加默认步骤
    if len(new_steps) == 0:
        step = {"id": "1", "dependence": [], "tool": None,
                "analysis": "当前问题超出了可处理范围，建议寻求值班同学介入处理。"}
        new_steps.append(step)

    return new_steps
