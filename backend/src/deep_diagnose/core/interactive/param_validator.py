import re

from deep_diagnose.common.utils.string_utils import is_blank, contains_keywords


def validate_params(params: dict, context: str) -> str | None:
    """
    验证参数中的关键字段值是否存在于上下文中。

    该函数主要用于确保参数中与虚拟机（VM）、宿主机（NC）、实例或机器相关的字段值
    在给定的上下文中存在，以防止无效的参数传入工具调用。

    参数:
        params (dict): 需要验证的参数字典。
        context (str): 上下文字符串，通常包含有效的字段值（如VM ID、NC IP等）。

    返回:
        Optional[str]: 如果发现无效字段值，返回错误信息；否则返回 None。
    """
    # 定义需验证的关键字集合
    keywords = ["vm", "nc", "instance", "machine"]

    # 遍历参数字典中的每一个键值对
    for key in params:
        # 如果键名不含关键字，则跳过
        if not contains_keywords(key, keywords):
            continue
        param_value = params[key]
        # 统一转为列表处理
        values = [param_value] if isinstance(param_value, str) else param_value
        # 具体执行校验
        for item in values:
            error = _validate_param_value(item, context)
            if error is not None:
                return error
    # 所有参数验证通过
    return None


def _validate_param_value(param_value: str, context: str) -> str | None:
    if is_blank(param_value):
        return "param value is blank"
    if param_value not in context:
        return f"{param_value} is not found in context"
    if re.fullmatch(r'[A-Za-z0-9.-]+', param_value) is None:
        return f"{param_value} has invalid characters."
    return None
