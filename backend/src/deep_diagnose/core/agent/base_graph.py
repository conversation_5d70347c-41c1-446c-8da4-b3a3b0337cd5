"""Agent基类定义

定义所有Graph Agent的抽象基类和统一接口。
"""

import abc
from typing import AsyncIterator, Dict, Any, List, Optional
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent


class BaseGraph(abc.ABC):
    """
    所有 Graph Agent 的抽象基类。
    定义了统一的接口和所有 Graph 必须具备的基础属性。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化基础Graph

        Args:
            config: 配置字典，包含Graph运行时的各种参数
        """
        config = config or {}

        # ------------------------------------------------------------------
        # 核心要求：将 Graph 状态和配置作为其内部属性
        # ------------------------------------------------------------------

        # 运行时状态 - 只保留核心状态变量
        self.plan_iterations: int = 0
        self.final_report: str = ""
        self.current_plan: Optional[list] = None
        self.observations: list = []

    @abc.abstractmethod
    async def astream(self, question: str, messages: Optional[List[Dict[str, Any]]] = None, **kwargs) -> AsyncIterator[BaseAgentOutputEvent]:
        """
        Graph 的主执行方法，以异步生成器方式流式返回事件。

        Args:
            question: 用户输入的问题
            messages: 历史消息列表
            **kwargs: 其他可选参数

        Yields:
            BaseAgentOutputEvent: 智能体输出事件对象
        """
        # 确保子类实现该方法
        pass
