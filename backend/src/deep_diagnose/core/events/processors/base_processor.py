"""
基础消息处理器

定义消息处理器的抽象接口，重命名自 BaseAgentEventProcessor
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List

from ..models import AgentMessage

logger = logging.getLogger(__name__)


class BaseMessageProcessor(ABC):
    """基础消息处理器 - 更准确地反映处理的是消息而非事件"""

    @abstractmethod
    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理特定Agent的消息"""
        pass

    @abstractmethod
    def get_agent_name(self) -> str:
        """获取处理的Agent名称"""
        pass