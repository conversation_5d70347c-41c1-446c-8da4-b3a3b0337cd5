"""
Agent V2 事件模型

简化的事件模型，主要包含一个 ReasoningAgentEvent 类。
"""

import json
import logging
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessageChunk, ToolMessage

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.core.reasoning.report_generator import create_html_generator, create_auto_generator
from deep_diagnose.common.utils.json_utils import repair_json_output
# 2. 具体事件子类定义
@dataclass
@BaseAgentOutputEvent.register_event_type("reasoning_event_deprecated")
class ReasoningAgentEventDeprecated(BaseAgentOutputEvent):
    """推理智能体事件 - 累积式状态容器"""
    thought: str = ""                           # 累积所有思考内容
    plan_steps: List[Dict[str, Any]] = field(default_factory=list)  # 计划步骤
    executions: List[Dict[str, Any]] = field(default_factory=list)  # 工具执行记录
    result: str = ""                            # 累积所有结果内容
    urls: List[Dict[str, Any]] = field(default_factory=list)

    # 内部缓冲区（不对外暴露）
    _planner_buffer: str = field(default="", init=False)
    _tool_calls: Dict[str, Dict] = field(default_factory=dict, init=False)
    _tool_call_ids: Dict[str, str] = field(default_factory=dict, init=False)

    def parse(self, raw_event: str) -> bool:
        """
        解析原始 SSE 事件（保留以满足基类要求，但主要使用 parse_graph_event）
        
        Args:
            raw_event: 原始 SSE 事件字符串
            
        Returns:
            bool: 是否成功解析并更新了状态
        """
        # 简化实现，主要功能已移到 parse_graph_event
        return False

    def parse_graph_event(self, agent_path, event_data, thread_id: str) -> bool:
        """
        解析 LangGraph 事件并累积更新状态

        增强版本，完全参考内层和外层事件处理逻辑，确保 thought, plan, result, urls, execution 信息准确保存

        Args:
            agent_path: 智能体路径，如 ('planner',) 或 ('researcher',)
            event_data: LangGraph 事件数据，通常是 (message, metadata) 元组
            thread_id: 线程ID

        Returns:
            bool: 是否成功更新了状态
        """
        logger = logging.getLogger(__name__)

        try:
            # 1. 处理中断事件（参考内层 graph workflow 的 event_data 处理逻辑）
            if isinstance(event_data, dict) and "__interrupt__" in event_data:
                logger.debug(f"Ignoring interrupt event from {agent_path}")
                return False

            # 2. 验证事件数据格式并处理静默标签过滤
            if not self._is_valid_event(event_data):
                return False

            # 3. 提取智能体名称
            agent_name = self._extract_agent_name(agent_path)
            if not agent_name:
                return False

            # 4. 处理消息事件（参考内层 graph workflow 的消息处理逻辑）
            if self._is_message_event(event_data):
                message_chunk, message_metadata = event_data
                content = getattr(message_chunk, 'content', '') or ""
                finish_reason = message_chunk.response_metadata.get("finish_reason") if hasattr(message_chunk, 'response_metadata') else message_metadata.get('finish_reason')

                # 根据消息类型处理不同的事件（参考内层逻辑）
                if isinstance(message_chunk, ToolMessage):
                    # 工具消息 - 返回工具调用结果
                    return self._handle_tool_message(message_chunk, agent_name)

                elif isinstance(message_chunk, AIMessageChunk):
                    # AI消息处理
                    if message_chunk.tool_calls:
                        # AI消息 - 工具调用
                        return self._handle_ai_tool_calls(message_chunk, agent_name)
                    elif message_chunk.tool_call_chunks:
                        # AI消息 - 工具调用块
                        return self._handle_ai_tool_call_chunks(message_chunk, agent_name)
                    else:
                        # AI消息 - 原始消息令牌
                        return self._handle_ai_message_content(content, finish_reason, agent_name)
                else:
                    # 其他类型的消息，按内容处理
                    return self._handle_ai_message_content(content, finish_reason, agent_name)

            return False

        except Exception as e:
            logger.error(f"Parse graph event error: {e}", exc_info=True)
            return False

    def _handle_tool_message(self, message_chunk, agent_name: str) -> bool:
        """处理工具消息 - 工具调用结果"""
        tool_call_id = getattr(message_chunk, 'tool_call_id', None)
        content = getattr(message_chunk, 'content', '')

        if tool_call_id and tool_call_id in self._tool_calls:
            # 检查是否是被过滤的工具
            tool_name = self._tool_calls[tool_call_id].get("tool_name", "")
            if tool_name == "handoff_to_planner":
                logging.getLogger(__name__).debug(f"Ignoring tool result for: {tool_name}")
                return False

            try:
                # 尝试解析 JSON 结果
                parsed_result = json.loads(content) if content else None
                result_to_store = parsed_result
            except json.JSONDecodeError:
                # 解析失败时保存原始内容
                result_to_store = content

            # 更新 _tool_calls 中的结果和状态
            self._tool_calls[tool_call_id]["result"] = result_to_store
            self._tool_calls[tool_call_id]["status"] = "completed"

            # 同时更新 executions 列表中对应的记录
            for exec_record in self.executions:
                if exec_record.get("tool_call_id") == tool_call_id:
                    exec_record["result"] = result_to_store
                    exec_record["status"] = "completed"
                    break

            return True
        return False

    def _handle_ai_tool_calls(self, message_chunk, agent_name: str) -> bool:
        """处理 AI 消息 - 工具调用"""
        if not hasattr(message_chunk, 'tool_calls') or not message_chunk.tool_calls:
            return False

        has_valid_tool_calls = False
        tool_run_id = message_chunk.id
        for tool_call in message_chunk.tool_calls:
            tool_call_id = tool_call.get("id", "")
            tool_name = tool_call.get("name", "")

            # 过滤 handoff_to_planner 工具调用
            if tool_name == "handoff_to_planner":
                logging.getLogger(__name__).debug(f"Ignoring tool call: {tool_name}")
                continue

            if tool_call_id and tool_call_id not in self._tool_calls:
                # 创建工具执行记录
                execution_record = {
                    "tool_call_id": tool_call_id,
                    "tool_name": tool_name,
                    "parameters": tool_call.get("args", {}),
                    "result": None,
                    "status": "started",
                    "_args_chunks": []  # 用于累积参数块
                }
                self._tool_calls[tool_call_id] = execution_record
                self.executions.append(execution_record)
                self._tool_call_ids[tool_run_id] = tool_call_id
                has_valid_tool_calls = True
        if hasattr(message_chunk, 'tool_call_chunks') and message_chunk.tool_call_chunks:
            self._handle_ai_tool_call_chunks(message_chunk, agent_name)
        # 清理被过滤的工具调用
        self._cleanup_filtered_tools()
        return has_valid_tool_calls

    def _handle_ai_tool_call_chunks(self, message_chunk, agent_name: str) -> bool:
        """处理 AI 消息 - 工具调用块"""
        if not hasattr(message_chunk, 'tool_call_chunks') or not message_chunk.tool_call_chunks:
            return False

        updated = False
        tool_run_id = message_chunk.id
        for chunk in message_chunk.tool_call_chunks:
            tool_call_id = chunk.get("id", None)
            if not tool_call_id and tool_run_id in self._tool_call_ids:
                tool_call_id = self._tool_call_ids[tool_run_id]
            
            if not tool_call_id:
                # 如果无法获取tool_call_id，跳过这个chunk
                logging.getLogger(__name__).debug(f"Skipping tool call chunk without valid tool_call_id for run_id: {tool_run_id}")
                continue
                
            if tool_call_id and tool_call_id in self._tool_calls:
                # 检查是否是被过滤的工具
                tool_name = self._tool_calls[tool_call_id].get("tool_name", "")
                if tool_name == "handoff_to_planner":
                    continue  # 跳过被过滤的工具

                args_value = chunk.get("args", "")
                # 参考 sse_message_parser.py 的处理方式
                args_str = json.dumps(args_value, ensure_ascii=False) if isinstance(args_value, dict) else str(args_value or "")
                self._tool_calls[tool_call_id]["_args_chunks"].append(args_str)

                # 尝试解析完整的参数并更新到 executions 中
                try:
                    combined_args = "".join(self._tool_calls[tool_call_id]["_args_chunks"])
                    if combined_args.strip():
                        repaired_json = repair_json_output(combined_args)
                        parsed_params = json.loads(repaired_json)

                        # 更新 _tool_calls 中的参数
                        self._tool_calls[tool_call_id]["parameters"] = parsed_params

                        # 同时更新 executions 列表中对应的记录
                        for exec_record in self.executions:
                            if exec_record.get("tool_call_id") == tool_call_id:
                                exec_record["parameters"] = parsed_params
                                break

                        updated = True
                except Exception as e:
                    logging.getLogger(__name__).debug(f"Failed to parse tool args for {tool_call_id}: {e}")
                    pass  # 解析失败时保持原有参数

        return updated

    def _handle_ai_message_content(self, content: str, finish_reason: str, agent_name: str) -> bool:
        """处理 AI 消息内容"""
        if not content.strip() and not finish_reason:
            return False

        # 根据智能体类型累积内容（参考 sse_message_parser.py 的逻辑）
        if agent_name == "planner":
            if content.strip():
                self._planner_buffer += content

            # 当 planner 完成时，解析计划
            if finish_reason == "stop":
                self._parse_and_accumulate_planner()
                return True

            return bool(content.strip())

        elif agent_name in ["researcher", "coder", "background_investigator"]:
            return True

        elif agent_name in ["reporter", "coordinator"]:
            if content.strip():
                # 累积到 result 字段
                self.result += content
                return True

        return False


    def _add_url_if_not_exists(self, url: str, name: str) -> None:
        """添加 URL 到 urls 列表（如果不存在）"""
        if not url or not url.strip():
            return

        # 检查是否已存在相同的 URL
        for existing_url in self.urls:
            if existing_url.get("url") == url:
                return

        self.urls.append({
            "name": name,
            "url": url
        })

    def _cleanup_filtered_tools(self):
        """清理被过滤的工具调用记录"""
        # 定义需要过滤的工具名称
        filtered_tool_names = {"handoff_to_planner"}

        # 从 executions 列表中移除被过滤的工具
        self.executions = [
            exec_record for exec_record in self.executions
            if exec_record.get("tool_name") not in filtered_tool_names
        ]

        # 从 _tool_calls 字典中移除被过滤的工具
        filtered_tool_ids = [
            tool_id for tool_id, tool_data in self._tool_calls.items()
            if tool_data.get("tool_name") in filtered_tool_names
        ]

        for tool_id in filtered_tool_ids:
            del self._tool_calls[tool_id]

    def _is_valid_event(self, event_data) -> bool:
        """验证事件数据格式"""
        # 处理静默标签过滤（参考内层 graph workflow 的 event_data 处理逻辑）
        if isinstance(event_data, tuple) and len(event_data) == 2:
            message, metadata = event_data

            # 检查元数据中是否包含静默标签
            if "silent_llm_call" in metadata.get("tags", []):
                logging.getLogger(__name__).debug("Ignoring event with tag 'silent_llm_call'.")
                return False

            # 检查是否包含 handoff_to_planner 标签并过滤
            if "handoff_to_planner" in metadata.get("tags", []):
                logging.getLogger(__name__).debug("Ignoring event with tag 'handoff_to_planner'.")
                return False

        return True

    def _extract_agent_name(self, agent_path) -> str:
        """从 agent_path 提取智能体名称"""
        if isinstance(agent_path, tuple) and len(agent_path) > 0:
            return agent_path[0].split(":")[0]
        return ""

    def _is_message_event(self, event_data) -> bool:
        """判断是否为消息事件"""
        return isinstance(event_data, tuple) and len(event_data) == 2

    def _parse_and_accumulate_planner(self):
        """
        解析planner缓冲区并累积到thought和plan_steps

        增强版本，完全参考 sse_message_parser.py 的 _parse_planner_plan 方法
        """
        if not self._planner_buffer.strip():
            logging.getLogger(__name__).warning("Attempted to parse planner JSON, but no content was collected.")
            return

        logger = logging.getLogger(__name__)
        combined_content = self._planner_buffer
        logger.info(f"Attempting to parse complete planner JSON ({len(combined_content)} chars).")

        try:
            # 使用与 sse_message_parser.py 相同的修复逻辑

            repaired_json = repair_json_output(combined_content)
            parsed_plan = json.loads(repaired_json)

            # 验证解析结果的结构
            if "thought" in parsed_plan and "steps" in parsed_plan:
                # 处理 thought 字段
                if isinstance(parsed_plan["thought"], str):
                    planner_thought = parsed_plan["thought"].strip()
                    if planner_thought:
                        if self.thought:
                            self.thought += "\n\n" + planner_thought
                        else:
                            self.thought = "" + planner_thought

                # 处理 steps 字段
                if isinstance(parsed_plan["steps"], list):
                    # 清空现有计划步骤，使用新解析的步骤
                    self.plan_steps = []
                    for i, step in enumerate(parsed_plan["steps"]):
                        if isinstance(step, dict):
                            step_record = {
                                "step": i + 1,
                                "title": step.get("title", ""),
                                "description": step.get("description", "")
                            }
                            self.plan_steps.append(step_record)

                logger.info(f"Successfully parsed complete planner plan with {len(self.plan_steps)} steps.")
                if self.plan_steps:
                    step_titles = [step.get('title', '') for step in self.plan_steps]
                    logger.info(f"Planner plan steps: {step_titles}")

            else:
                logger.warning("Parsed planner JSON is missing 'thought' or 'steps'.")
                # 即使结构不完整，也尝试提取可用信息
                self._extract_partial_planner_info(parsed_plan)

        except Exception as e:
            logger.error(f"Planner JSON parse failed: {e}")
            # 解析失败时，将原始内容作为 thought 保存
            raw_content = f"[Planner Raw] {self._planner_buffer}"
            if self.thought:
                self.thought += "\n\n" + raw_content
            else:
                self.thought = raw_content

    def _extract_partial_planner_info(self, parsed_plan: dict):
        """从部分解析的 planner 数据中提取可用信息"""
        logger = logging.getLogger(__name__)

        # 尝试提取 thought
        if "thought" in parsed_plan and isinstance(parsed_plan["thought"], str):
            thought_content = parsed_plan["thought"].strip()
            if thought_content:
                if self.thought:
                    self.thought += "\n\n" + thought_content
                else:
                    self.thought = "" + thought_content

        # 尝试提取 steps
        if "steps" in parsed_plan and isinstance(parsed_plan["steps"], list):
            for i, step in enumerate(parsed_plan["steps"]):
                if isinstance(step, dict):
                    step_record = {
                        "step": i + 1,
                        "title": step.get("title", ""),
                        "description": step.get("description", "")
                    }
                    self.plan_steps.append(step_record)

            if self.plan_steps:
                logger.info(f"Extracted {len(self.plan_steps)} steps from partial planner data.")

        # 如果有其他有用的字段，也可以提取
        for key, value in parsed_plan.items():
            if key not in ["thought", "steps"] and isinstance(value, str) and value.strip():
                logger.debug(f"Found additional planner field '{key}': {value[:100]}...")
                # 可以根据需要处理其他字段

    def add_report_url(self, report_path: str) -> None:
        """添加报告 URL

        Args:
            report_path: 报告路径
        """
        self._add_url_if_not_exists(report_path, "CloudBot智能体-长推理诊断报告")

    def generate_and_upload_report(self, task_id: str) -> None:
        """
        生成 HTML 报告并上传到 OSS，更新 URLs

        参考 task_executor.py 的实现逻辑

        Args:
            task_id: 任务ID
        """
        try:
            # 只有当有结果内容时才生成报告
            if not self.result.strip():
                logging.getLogger(__name__).debug(f"No result content for task {task_id}, skipping report generation")
                return

            # 生成 HTML 报告
            report_url = self._generate_report(task_id, self.result)

            if report_url:
                # 添加报告 URL 到 urls 列表
                self.add_report_url(report_url)
                logging.getLogger(__name__).info(f"Task {task_id}: Report generated and added to URLs: {report_url}")
            else:
                logging.getLogger(__name__).warning(f"Task {task_id}: Failed to generate report")

        except Exception as e:
            logging.getLogger(__name__).error(f"Task {task_id}: Error generating report: {e}", exc_info=True)

    def _generate_report(self, task_id: str, report: str, report_type="html") -> str | None:
        """
        生成 HTML 报告并上传到 OSS

        参考 task_executor.py 的 _generate_report 方法

        Args:
            task_id: 任务ID
            report: 诊断报告内容
            report_type: 报告类型，默认为 "html"

        Returns:
            OSS URL 或本地文件路径，如果生成失败则返回 None
        """
        try:
            if report_type == "html":
                # 生成HTML（启用OSS上传）
                html_generator = create_html_generator(enable_oss=True)
                result = html_generator.generate_simple_report(task_id=task_id, content=report)
            elif report_type == "pdf":
                pdf_generator = create_auto_generator(enable_oss=True)
                result = pdf_generator.generate_simple_report(task_id=task_id, content=report)
            else:
                logging.getLogger(__name__).error(f"Unsupported report type: {report_type}")
                return None

            if result.success:
                # 优先返回OSS URL，如果没有则返回本地路径
                final_path = result.oss_url or result.file_path
                logging.getLogger(__name__).info(f"Generated report for task {task_id}: {final_path}")
                if result.oss_url:
                    logging.getLogger(__name__).info(f"Report uploaded to OSS: {result.oss_url}")
                return  result.oss_url
            else:
                logging.getLogger(__name__).error(f"Failed to generate report for task {task_id}: {result.error_message}")
                return None

        except Exception as e:
            logging.getLogger(__name__).error(f"Report generation failed for task {task_id}: {e}", exc_info=True)
            return None

    def to_sse_format(self) -> str:
        """
        将推理事件序列化为 SSE 格式

        修复版本：
        1. 移除重复的 "data:" 前缀
        2. 删除 structured_detail 字段
        3. 确保 executions 的 parameters 正确填充
        """
        # 确保在序列化前清理被过滤的工具
        self._cleanup_filtered_tools()

        # 过滤执行记录，确保不包含被过滤的工具，并确保参数正确填充
        filtered_executions = []
        for exec_item in self.executions:
            if exec_item.get("tool_name") != "handoff_to_planner":
                # 确保参数正确填充
                tool_call_id = exec_item.get("tool_call_id", "")
                if tool_call_id in self._tool_calls:
                    # 从 _tool_calls 中获取最新的参数
                    exec_item["parameters"] = self._tool_calls[tool_call_id].get("parameters", {})

                filtered_executions.append({
                    "tool_call_id": exec_item.get("tool_call_id", ""),
                    "tool_name": exec_item.get("tool_name", ""),
                    "parameters": exec_item.get("parameters", {}),
                    "result": exec_item.get("result"),
                    "status": exec_item.get("status", "unknown")
                })

        payload = {
            "thought": self.thought,
            "plan_steps": self.plan_steps,
            "executions": filtered_executions,
            "result": self.result,
            "urls": self.urls,
            "event_type": self.event_type
        }

        return json.dumps(payload, ensure_ascii=False)

    def _is_final_event(self, event_data: str) -> bool:
        """判断是否为最终事件"""
        try:
            data = json.loads(event_data)
            # 如果包含error或者result不为空且没有其他处理中的标识，认为是最终事件
            return bool(data.get("urls"))
        except json.JSONDecodeError:
            # 非JSON数据认为是最终事件
            return True

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional['ReasoningAgentEventDeprecated']:
        """从 SSE 格式反序列化推理事件"""
        try:
            data = json.loads(event_data)
            event = cls()

            # 填充各个字段
            event.thought = data.get("thought", "")
            event.plan_steps = data.get("plan_steps", [])
            event.executions = data.get("executions", [])
            event.result = data.get("result", "")
            event.urls = data.get("urls", [])

            return event
        except Exception:
            return None



