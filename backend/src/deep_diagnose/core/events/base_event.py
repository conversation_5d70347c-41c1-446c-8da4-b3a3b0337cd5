"""
Agent V2 事件模型

简化的事件模型，主要包含一个 ReasoningAgentEvent 类。
"""

import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Optional, Dict, Type


# 1. 自定义 JSON 编码器
class EnumEncoder(json.JSONEncoder):
    """自定义 JSON 编码器，支持枚举类型序列化"""

    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)


# 2. 基类定义
class BaseAgentOutputEvent(ABC):
    """所有 Agent 输出事件的抽象基类。"""

    # 事件类型注册表（类变量）
    _event_registry: Dict[str, Type["BaseAgentOutputEvent"]] = {}

    @classmethod
    def register_event_type(cls, event_type: str):
        """注册事件类型的装饰器"""

        def decorator(event_class):
            BaseAgentOutputEvent._event_registry[event_type] = event_class
            event_class._event_type = event_type
            return event_class

        return decorator

    @property
    def event_type(self) -> str:
        """获取当前事件的类型"""
        return getattr(self.__class__, "_event_type", self.__class__.__name__)

    def _is_final_event(self, event_data: str) -> bool:
        """
        判断是否为最终事件（基类默认实现）

        Args:
            event_data: SSE 格式的事件数据字符串

        Returns:
            bool: 是否为最终事件
        """
        try:
            data = json.loads(event_data)
            # 基类默认实现：如果包含error，认为是最终事件
            return bool(data.get("error"))
        except json.JSONDecodeError:
            # 非JSON数据认为是最终事件
            return True

    @abstractmethod
    def parse(self, raw_event: str) -> bool:
        """
        解析原始事件并更新自身状态

        Args:
            raw_event: 原始 SSE 事件字符串

        Returns:
            bool: 是否成功解析并更新了状态
        """
        pass

    def to_sse_format(self) -> str:
        """将事件对象序列化为 SSE 格式的字符串。"""
        # 使用 vars() 将 dataclass 实例转换为字典，但过滤掉以下划线开头的私有属性
        payload = {k: v for k, v in vars(self).items() if not k.startswith("_")}
        # 添加事件类型信息
        payload["event_type"] = self.event_type
        return json.dumps(payload, ensure_ascii=False, cls=EnumEncoder)

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional["BaseAgentOutputEvent"]:
        """
        从 SSE 格式的字符串反序列化为事件对象

        Args:
            event_data: SSE 格式的事件数据字符串

        Returns:
            BaseAgentOutputEvent: 反序列化后的事件对象，如果解析失败则返回 None
        """
        try:
            data = json.loads(event_data)

            # 获取事件类型
            event_type = data.get("event_type")
            if event_type and event_type in cls._event_registry:
                # 根据事件类型创建对应的类实例
                event_class = cls._event_registry[event_type]
                event = event_class()
            else:
                # 如果没有事件类型或类型未注册，且当前类不是抽象类，使用当前类
                if not getattr(cls, "__abstractmethods__", None):
                    event = cls()
                else:
                    # 如果是抽象基类，返回 None
                    import logging

                    logging.getLogger(__name__).warning(f"Cannot instantiate abstract class {cls.__name__} for unknown event type: {event_type}")
                    return None

            # 将解析的数据填充到事件对象中（排除 event_type）
            for key, value in data.items():
                if key != "event_type" and hasattr(event, key):
                    setattr(event, key, value)

            return event
        except Exception as e:
            import logging

            logging.getLogger(__name__).error(f"Failed to parse event data: {e}")
            return None

    def to_persist_format(self) -> dict[str, str]:
        """转换为持久化格式"""
        return {"message": self.to_sse_format()}


# 简化的事件类 - 只保留必要的数据结构
@dataclass
@BaseAgentOutputEvent.register_event_type("error")
class ErrorEvent(BaseAgentOutputEvent):
    """错误事件"""

    error: str = ""

    def parse(self, raw_event: str) -> bool:
        return False

    def _is_final_event(self, event_data: str) -> bool:
        """判断是否为最终事件（错误事件实现）"""
        try:
            data = json.loads(event_data)
            # 错误事件总是最终事件
            return bool(data.get("error"))
        except json.JSONDecodeError:
            return True

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional["ErrorEvent"]:
        """从 SSE 格式反序列化错误事件"""
        try:
            data = json.loads(event_data)
            if "error" in data:
                return cls(error=data["error"])
            return None
        except Exception:
            return None


@dataclass
@BaseAgentOutputEvent.register_event_type("status")
class StatusEvent(BaseAgentOutputEvent):
    """状态事件"""

    status: str = ""
    message: str = ""

    def parse(self, raw_event: str) -> bool:
        return False

    def _is_final_event(self, event_data: str) -> bool:
        """判断是否为最终事件（状态事件实现）"""
        try:
            data = json.loads(event_data)
            # 状态事件：completed, failed, error 状态认为是最终事件
            status = data.get("status", "").lower()
            return status in ["completed", "failed", "error", "finished", "done"]
        except json.JSONDecodeError:
            return True

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional["StatusEvent"]:
        """从 SSE 格式反序列化状态事件"""
        try:
            data = json.loads(event_data)
            if "status" in data:
                return cls(status=data["status"], message=data.get("message", ""))
            return None
        except Exception:
            return None
