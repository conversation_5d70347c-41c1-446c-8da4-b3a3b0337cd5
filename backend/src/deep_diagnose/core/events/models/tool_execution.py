"""
工具执行模型

工具调用和执行状态的数据结构，重命名自 ToolCall
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from enum import Enum


class ToolExecutionStatus(Enum):
    """工具执行状态"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ToolExecution:
    """工具执行信息 - 更准确地反映工具调用和执行的概念"""
    call_id: str
    call_name: str
    call_args: str = ""  # 设置默认值为空字符串，防止 None 值
    call_result: Optional[Dict[str, Any]] = None
    status: ToolExecutionStatus = ToolExecutionStatus.PENDING
    error_message: Optional[str] = None

    def mark_completed(self, result: Dict[str, Any]):
        """标记为完成"""
        self.status = ToolExecutionStatus.COMPLETED
        self.call_result = result

    def mark_failed(self, error: str):
        """标记为失败"""
        self.status = ToolExecutionStatus.FAILED
        self.error_message = error