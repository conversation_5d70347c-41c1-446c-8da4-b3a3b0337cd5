"""
用户领域模型定义
"""
from tortoise.models import Model
from tortoise import fields


class CloudbotAgentUser(Model):
    """用户信息表"""
    
    id = fields.BigIntField(pk=True, description="自增主键")
    username = fields.CharField(max_length=64, unique=True, description="用户名/员工工号")
    password = fields.CharField(max_length=255, description="哈希后的密码")
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(auto_now=True, description="更新时间")
    
    
    class Meta:
        table = "cloudbot_agent_user"
        table_description = "用户信息表"
    
    def __str__(self):
        return f"<CloudbotAgentUser(id={self.id}, username='{self.username}')>"