"""
聊天领域数据结构
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from deep_diagnose.domain.chat.models import MessageStatus


# Chat Session Schemas
class ChatSessionBase(BaseModel):
    """会话基础模型"""

    session_id: str = Field(..., max_length=64, description="会话id")
    user_id: str = Field(..., max_length=16, description="员工工号")
    subject: str = Field(default="", max_length=512, description="会话主题")
    ext: Dict[str, Any] = Field(default_factory=dict, description="扩展信息")
    source: str = Field(default="cloudbot_agent", max_length=32, description="消息来源")
    resource_ids: str = Field(default="", description="诊断资源ID列表")
    resource_types: str = Field(default="", max_length=256, description="诊断资源类型")


class ChatSessionCreate(ChatSessionBase):
    """创建会话模型"""

    pass


class ChatSessionUpdate(BaseModel):
    """更新会话模型"""

    session_id: Optional[str] = Field(None, max_length=64, description="会话id")
    user_id: Optional[str] = Field(None, max_length=16, description="员工工号")
    subject: Optional[str] = Field(None, max_length=512, description="会话主题")
    ext: Optional[Dict[str, Any]] = Field(None, description="扩展信息")
    source: Optional[str] = Field(None, max_length=32, description="消息来源")
    resource_ids: Optional[str] = Field(None, description="诊断资源ID列表")
    resource_types: Optional[str] = Field(None, max_length=256, description="诊断资源类型")


class ChatSession(ChatSessionBase):
    """会话响应模型"""

    id: int
    gmt_create: datetime
    gmt_modified: datetime

    class Config:
        from_attributes = True


# Chat Message Schemas
class ChatMessageBase(BaseModel):
    """消息基础模型"""

    session_id: str = Field(..., max_length=64, description="会话id")
    message: str = Field(..., description="聊天信息")
    message_type: str = Field(default="ai_response", max_length=16, description="消息类型")
    request_id: str = Field(..., max_length=64, description="请求ID")
    agent: str = Field(default="", max_length=32, description="消息处理 Agent 名称")
    ext: Dict[str, Any] = Field(default_factory=dict, description="扩展信息")
    status: str = Field(default=MessageStatus.EXECUTING.value, max_length=16, description="消息状态")


class ChatMessageCreate(ChatMessageBase):
    """创建消息模型"""

    pass


class ChatMessageUpdate(ChatMessageBase):
    """更新消息模型"""

    pass


class ChatMessage(ChatMessageBase):
    """消息响应模型"""

    id: int
    gmt_create: datetime
    gmt_modified: datetime

    class Config:
        from_attributes = True


# 历史记录查询响应模型
class ChatSessionHistory(BaseModel):
    """会话历史记录响应模型"""

    session_id: str = Field(..., description="会话ID")
    subject: str = Field(..., description="会话主题")
    time: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class ChatSessionHistoryResponse(BaseModel):
    """会话历史记录列表响应"""

    sessions: list[ChatSessionHistory] = Field(..., description="会话列表")
    total: int = Field(..., description="总数量")


class ChatMessageHistory(BaseModel):
    """消息历史记录响应模型"""

    id: int = Field(..., description="消息ID")
    message: str = Field(..., description="消息内容")
    message_type: str = Field(..., description="消息类型")
    request_id: str = Field(..., description="请求ID")
    agent: str = Field(..., description="消息处理 Agent 名称")
    gmt_create: datetime = Field(..., description="创建时间")
    status: int = Field(..., description="状态")

    class Config:
        from_attributes = True


class ChatMessagesResponse(BaseModel):
    """会话消息列表响应"""

    session_id: str = Field(..., description="会话ID")
    messages: list[ChatMessageHistory] = Field(..., description="消息列表")
    total: int = Field(..., description="消息总数")
