# -*- coding: utf-8 -*-
import os
import json
import logging
from typing import Dict
from logging.config import dictConfig

import os

APP_NAME = 'ecs-deep-diagnose'
IS_PRODUCTION_ENV = os.getenv('app_env', 'default') in ["pre", "pre1", "prod"]

if IS_PRODUCTION_ENV:
    work_path = f"/home/<USER>/{APP_NAME}/logs"
else:
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    work_path = os.path.join(base_dir, "./logs")
if not os.path.exists(work_path):
    os.makedirs(work_path)


def create_file_handler_config(filename: str, formatter: str = 'default', level: str = 'DEBUG') -> dict:
    """创建文件处理器配置"""
    return {
        "class": "logging.handlers.RotatingFileHandler",
        "level": level,
        "formatter": formatter,
        "filename": os.path.join(work_path, filename),
        'maxBytes': 1024 * 1024 * 30,  # 日志大小 30M
        "backupCount": 30,
        'encoding': 'utf-8',
    }


def create_logger_config(handler_name: str) -> dict:
    """创建日志记录器配置"""
    return {
        "level": "DEBUG",
        "handlers": [handler_name],
        "propagate": True
    }


# 日志配置
log_config = {
    "version": 1,
    "formatters": {
        "default": {
            "format": '%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(message)s'
        },
        "simple": {
            "format": u'%(message)s'
        },
    },
    "handlers": {
        "app_detail_handler": create_file_handler_config("app_detail.log"),
        "api_request_handler": create_file_handler_config("api_request.log", formatter="simple"),
        "not_chat_api_handler": create_file_handler_config("not_chat_api_request.log", formatter="simple"),
        "log_api_tool_handler": create_file_handler_config("log_api_tool_request.log", formatter="simple"),
        "model_request_handler": create_file_handler_config("model_request.log", formatter="simple"),
        "model_evaluation_handler": create_file_handler_config("model_evaluation.log", formatter="simple"),
        "component_request_handler": create_file_handler_config("component_request.log", formatter="simple"),
        'wsgi': {
            'class': 'logging.StreamHandler',
            'formatter': 'default',
        }
    },
    "loggers": {
        "app_detail": create_logger_config("app_detail_handler"),
        "api_request": create_logger_config("api_request_handler"),
        "not_chat_api_request": create_logger_config("not_chat_api_handler"),
        "log_api_tool_request": create_logger_config("log_api_tool_handler"),
        "model_request": create_logger_config("model_request_handler"),
        "model_evaluation": create_logger_config("model_evaluation_handler"),
        "component_request": create_logger_config("component_request_handler"),
    },
    'root': {
        'level': 'INFO',
        'handlers': ['wsgi']
    }
}

# 设置日志配置
dictConfig(log_config)

app_detail_logger = logging.getLogger('app_detail')
api_request_logger = logging.getLogger("api_request")
not_chat_api_request_logger = logging.getLogger("not_chat_api_request")
log_api_tool_request_logger = logging.getLogger("log_api_tool_request")
model_request_logger = logging.getLogger("model_request")
model_evaluation_logger = logging.getLogger("model_evaluation")
component_request_logger = logging.getLogger("component_request")
mcp_request_logger = logging.getLogger("mcp_request")


def app_detail(
        request_id: str,
        func: str,
        category: str,
        log_detail
) -> None:
    if not isinstance(log_detail, str):
        log_detail = str(log_detail)
    app_detail_logger.info('|'.join([
        request_id or '',
        func,
        category,
        log_detail.replace("\n", "\\n")
    ]))


def log_api_request(
        request_id: str,
        session_id: str,
        ak_name: str,
        user_id: str,
        agent: str,
        question: str,
        request_source: str,
        start_timestamp: int,
        end_timestamp: int,
        first_token_timestamp: int,
        answer: str,
        status: str,
        error_message: str,
        user_agent: str,
        worker_agent: str
) -> None:
    """Log details about API requests."""
    details = [
        request_id,
        session_id,
        ak_name,
        user_id,
        agent,
        question,
        start_timestamp,
        end_timestamp,
        first_token_timestamp,
        answer,
        status,
        error_message,
        request_source,
        user_agent,
        worker_agent
    ]
    safe_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    api_request_logger.info('|'.join(safe_details))
    for handler in api_request_logger.handlers:
        handler.flush()


def log_not_chat_api_request(
        api_url: str,
        api_method: str,
        user_id: str,
        start_timestamp: int,
        end_timestamp: int,
        status: str,
        error_message: str,
) -> None:
    """Log details about API requests."""
    details = [
        api_url,
        api_method,
        user_id,
        start_timestamp,
        end_timestamp,
        status,
        error_message
    ]
    safe_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    not_chat_api_request_logger.info('|'.join(safe_details))
    for handler in not_chat_api_request_logger.handlers:
        handler.flush()


def log_api_tool_request(
        api_url: str,
        api_method: str,
        start_timestamp: int,
        end_timestamp: int,
        status: str,
        error_message: str,
        endpoint: str,
) -> None:
    """Log details about API requests."""
    details = [
        api_url,
        api_method,
        start_timestamp,
        end_timestamp,
        status,
        error_message,
        endpoint
    ]
    safe_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    log_api_tool_request_logger.info('|'.join(safe_details))
    for handler in log_api_tool_request_logger.handlers:
        handler.flush()


def log_model_request(
        model_name: str,
        request_id: str,
        model_request_id: str,
        start_timestamp: int,
        first_token_timestamp: int,
        end_timestamp: int,
        status: str,
        error_message: str = ""
) -> None:
    """Log details about model requests."""
    details = [
        model_name,
        request_id,
        model_request_id,
        start_timestamp,
        first_token_timestamp,
        end_timestamp,
        status,
        error_message
    ]
    safe_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    model_request_logger.info("|".join(safe_details))
    for handler in model_request_logger.handlers:
        handler.flush()


def log_model_evaluation(
        request_id: str,
        start_timestamp: int,
        end_timestamp: int,
        problems: Dict,
        metrics: Dict
) -> None:
    """Log details about model evaluations."""
    problems_json = json.dumps(problems, ensure_ascii=False)
    metrics_json = json.dumps(metrics, ensure_ascii=False)
    details = [request_id, start_timestamp, end_timestamp, problems_json, metrics_json]
    safe_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    model_evaluation_logger.info("|".join(safe_details))
    for handler in model_evaluation_logger.handlers:
        handler.flush()


def log_component_request(
        request_id: str,
        component_type: str,
        run_id: str,
        run_name: str,
        start_timestamp: int,
        end_timestamp: int,
        status: str,
        error_message: str = ""
) -> None:
    details = [request_id, component_type, run_id, run_name, start_timestamp, end_timestamp, status, error_message]
    exception_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    component_request_logger.info("|".join(exception_details))
    for handler in component_request_logger.handlers:
        handler.flush()

def log_mcp_request(
        start_timestamp: int,
        end_timestamp: int,
        server_name: str,
        request_method: str,
        request_body: dict
) -> None:
    details = [start_timestamp, end_timestamp, server_name, request_method, request_body]
    mcp_request_details = [str(detail).replace("|", "&#124;").replace('\n', '\\n') for detail in details]
    mcp_request_logger.info("|".join(mcp_request_details))
    for handler in mcp_request_logger.handlers:
        handler.flush()