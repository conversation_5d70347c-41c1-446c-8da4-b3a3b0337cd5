from datetime import datetime, timedelta


def current_time_with_week() -> str:
    """
    获取当前时间，并包含星期几信息。

    返回:
    - str: 当前时间字符串
    """
    now = datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S %A")


def offset_timestamp(timestamp_str: str, offset_seconds: int) -> str:
    """
    将给定的时间戳字符串按照指定的秒数偏移后，返回新的时间戳字符串。

    参数:
    - timestamp_str (str): 时间戳字符串，格式为 "yyyy-MM-dd HH:mm:ss"
    - offset_seconds (int): 偏移的秒数，可为正或负

    返回:
    - str: 偏移后的时间戳字符串，格式为 "yyyy-MM-dd HH:mm:ss"
    """
    # 将字符串解析为 datetime 对象
    dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

    # 偏移指定秒数
    dt += timedelta(seconds=offset_seconds)

    # 将结果转换回字符串格式
    return dt.strftime("%Y-%m-%d %H:%M:%S")
