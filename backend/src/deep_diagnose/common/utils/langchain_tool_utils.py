from typing import Dict, Any, cast
from typing import Dict, Any, cast
import logging

from langchain_core.tools import StructuredTool, BaseTool
from langchain_mcp_adapters.sessions import create_session
from langchain_mcp_adapters.tools import _convert_call_tool_result, NonTextContent
from mcp import ClientSession

logger = logging.getLogger(__name__)


def structured_tool_2_dict(
        tool: BaseTool
) -> Dict:
    return {
        "name": tool.name,
        "description": tool.description,
        "args_schema": tool.args_schema
    }


def dict_2_structured_tool(
        tool_info: Dict,
        mcp_server: Dict
) -> StructuredTool:
    async def call_tool(
            **arguments: dict[str, Any],
    ) -> tuple[list[str], list[NonTextContent]]:
        tool_name = tool_info.get("name")
        async with create_session(mcp_server) as tool_session:
            await tool_session.initialize()
            call_tool_result = await cast(ClientSession, tool_session).call_tool(
                tool_name, arguments
            )
        text_or_list, artifacts = _convert_call_tool_result(call_tool_result)
        # Normalize outputs to arrays to satisfy response_format validation
        try:
            if text_or_list is None:
                text_list: list[str] = []
            elif isinstance(text_or_list, str):
                text_list = [text_or_list]
            elif isinstance(text_or_list, list):
                text_list = text_or_list
            else:
                text_list = [str(text_or_list)]
            if artifacts is None:
                artifacts_list: list[NonTextContent] = []
            elif isinstance(artifacts, list):
                artifacts_list = artifacts
            else:
                artifacts_list = [artifacts]  # best-effort
        except Exception as e:
            logger.warning(f"Normalization of tool outputs failed: {e}; falling back to empty arrays")
            text_list, artifacts_list = [], []
        return text_list, artifacts_list
    return StructuredTool(
        name=tool_info.get("name"),
        description=tool_info.get("description"),
        args_schema=tool_info.get("args_schema"),
        coroutine=call_tool,
        response_format="content_and_artifact"
    )
