app:
  port: 8000
  secret: ecs_deep_diagnose
  name: ecs-deep-diagnose
  home_directory: /home/<USER>/ecs-deep-diagnose

# Workflow configuration
workflow:
  max_steps: 100
  auto_accept_plan: true
  enable_background_investigation: false

auth:
  # 数据库认证开关 (false: 使用YAML认证, true: 使用数据库认证)
  enable_database_auth: true
  buc_sso:
    host: https://login.alibaba-inc.com
    app_code: 40cbff03-d950-4b8e-8f38-e7b14f4c631d
  jwt_users:
    - user_name: admin
      password: admin
    - user_name: viewer
      password: viewer

mcp_servers:
  vm_coredump:
    protocol: streamable_http
    base_url: https://ecs-mcp.alibaba-inc.com
    path: /vm_coredump/mcp/
    token: ************************************************
    auth: token
    enabled_tools:
      - get_vm_coredump

  diagnose:
    protocol: streamable_http
    base_url: http://pre-xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /mcp/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - runLiveMigrationCheck
      - runColdMigrationCheck
      - listLiveMigrationRecords
      - listColdMigrationRecords
      - runOpsEventPostpone
      - runOperationSubmit
      - listChangeRecords
      - listActionTrail
      - listReportedOperationalEvents
      - runVmStartStopDiagnose
      - runScreenShotDiagnose
      - runPerformanceDiagnose
      - runDiagnose
      - listMonitorExceptions
      - listCategorizedMonitorExceptions
      - listHealthStatus
      - listVmsOnNc
      - listVmHostHistory
      - getUserInfo
      - getNcBasicInfo
      - getVmBasicInfo
      - getDiskInfo
      - listOnDutyStaffs
      - listOperationRecords
      - listOperationRuleMatchRecords
      - analyzeVmcoreFromCore
      - analyzeVmcoreFromVirt
      - analyzeVmcoreFromJinlun
      - getCrashLog
      - listKnowledge
      - runVmUnavailableDiagnose

  antv:
    protocol: stdio
    args:
      - -y
      - "@antv/mcp-server-chart"
    enabled_tools:
      - generate_pie_chart

security:
  # 定义用于解密的公钥名锚点(&keycenter_pub_name)，供全局引用
  key_center_public_name: &keycenter_pub_name ecs-deep-diagnose_aone_key

  # 密钥库：存储所有需要加密管理的密钥
  secrets_vault:
    qwen_api_key: &qwen_ak AKIsqn3/M5NSMshB4OJ6ZqdQwyEpYj4DiMdBjEtOjcZSQ1ulaGqinPQmZRYQm9wJ
    langfuse_secret_key: &langfuse_secret_key Jp9iO1h92IOBdD++hf0baAG0KuUGVJDYbKDfTXAAopKTrMlfX2pnqmUwLJSGcZNv
    langfuse_public_key: &langfuse_public_key blWQfahUx4KUxcLhdBprNYLUI7rsSEwe8PBid0uf4dXegx+KNErFY5ctczMunh/j
    osc_secret_key: &osc_secret_key KgcvuHxllFAd0lFX32Qhv/Cm7zH3WFkIAxU3GJa/i+E=
    redis_sk: &redis_sk ZWNzX2RlZXBfZGlhZ25vc2VAMTIzNA==
    rds_sk: &rds_sk 2+oo3wqbT+11XZ25A2Go3mAKDSgc8OOFl2c+mLUeNF8=

llm:
  tongyi_provider:
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    api_key: !decrypt [*qwen_ak, *keycenter_pub_name]

  profiles:
    reasoning:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen3-235b-a22b"
      api_key: !decrypt [*qwen_ak, *keycenter_pub_name]
    basic:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-max-latest"
      api_key: !decrypt [*qwen_ak, *keycenter_pub_name]
    vision:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-max-latest"
      api_key: !decrypt [*qwen_ak, *keycenter_pub_name]

observability:
  langfuse:
    public_key: !decrypt [*langfuse_public_key, *keycenter_pub_name]
    secret_key: !decrypt [*langfuse_secret_key, *keycenter_pub_name]
    endpoint: https://chatecs-trace.alibaba-inc.com

infrastructure:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    region: oss-cn-hangzhou
    bucket_name: long-reasoning
    access_key_id: LTAI5tBztNcv1ycZocRP7gKc
    access_key_secret: !decrypt [*osc_secret_key, *keycenter_pub_name]
    upload_dir: ecs-deep-diagnose/diagnostic-report

  redis:
    host: r-ecs-deep-diagnose.redis.rds.aliyuncs.com
    port: 6379
    user: r-ecs_deep_diagnose
    password: !decode &password [*redis_sk]
    broker_url:
      !join [
        "redis://r-ecs_deep_diagnose:",
        *password,
        "@r-ecs-deep-diagnose.redis.rds.aliyuncs.com:6379/2",
      ]
    result_backend_url:
      !join [
        "redis://r-ecs_deep_diagnose:",
        *password,
        "@r-ecs-deep-diagnose.redis.rds.aliyuncs.com:6379/3",
      ]
    refresh_time: 1800
  rds:
    host: rm-ecs-deep-diagnose.mysql.rds.aliyuncs.com
    port: 3306
    user: cloudbotagent_user
    pwd: !decrypt [*rds_sk, *keycenter_pub_name]
    db: cloudbotagent
