"""
Application-level constants.

This module contains constants related to the application itself,
such as application name, directories, and basic configuration.
"""

def _get_app_config():
    """Get application configuration from deep_diagnose.common.config system."""
    try:
        from deep_diagnose.common.config import get_config
        config = get_config()
        return config.app
    except (ImportError, AttributeError):
        # Fallback to default values if config is not available
        return None

def _get_app_name():
    """Get application name from deep_diagnose.common.config or default."""
    app_config = _get_app_config()
    if app_config and hasattr(app_config, 'name'):
        return app_config.name
    return 'ecs-deep-diagnose'  # Default fallback

def _get_app_home_directory():
    """Get application home directory from deep_diagnose.common.config or default."""
    app_config = _get_app_config()
    if app_config and hasattr(app_config, 'home_directory'):
        return app_config.home_directory
    return '/home/<USER>/ecs-deep-diagnose'  # Default fallback

# Application identity - use defaults to avoid import issues during module loading
APPLICATION_NAME = 'ecs-deep-diagnose'
APPLICATION_HOME_DIRECTORY = '/home/<USER>/ecs-deep-diagnose'

# For backward compatibility
APP_HOME_DIR = APPLICATION_HOME_DIRECTORY
# Field name constants
REQUEST_ID_FIELD = 'request_id'
THREAD_ID_FIELD = 'thread_id'  # Legacy field name, use REQUEST_ID_FIELD instead

__all__ = [
    'APPLICATION_NAME',
    'APPLICATION_HOME_DIRECTORY',
    'APP_HOME_DIR',  # Legacy name
    'REQUEST_ID_FIELD',
    'THREAD_ID_FIELD'
]
