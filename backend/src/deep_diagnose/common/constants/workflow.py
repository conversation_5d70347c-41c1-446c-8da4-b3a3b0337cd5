"""
Workflow-related constants.

This module contains constants that control workflow behavior,
such as step limits, auto-acceptance settings, and investigation features.
"""

def _get_workflow_config():
    """Get workflow configuration from deep_diagnose.common.config system."""
    try:
        from deep_diagnose.common.config import get_config
        config = get_config()
        return config.workflow
    except (ImportError, AttributeError):
        # Fallback to default values if config is not available
        return None

def _get_workflow_max_steps():
    """Get workflow max steps from deep_diagnose.common.config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'max_steps'):
        return workflow_config.max_steps
    return 20  # Default fallback

def _get_workflow_auto_accept_plan():
    """Get workflow auto accept plan setting from deep_diagnose.common.config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'auto_accept_plan'):
        return workflow_config.auto_accept_plan
    return True  # Default fallback

def _get_workflow_enable_background_investigation():
    """Get workflow background investigation setting from deep_diagnose.common.config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'enable_background_investigation'):
        return workflow_config.enable_background_investigation
    return False  # Default fallback


# For backward compatibility
DEFAULT_MAX_STEP_NUM = _get_workflow_max_steps()
DEFAULT_AUTO_ACCEPTED_PLAN = _get_workflow_auto_accept_plan()
DEFAULT_ENABLE_BACKGROUND_INVESTIGATION = _get_workflow_enable_background_investigation()
DEFAULT_MAX_RECURSIVE_NUM = 100
