"""
Built-in questions configuration constants.

This module contains predefined questions for the chat interface,
supporting both English and Chinese versions.
"""

# Built-in questions for the chat interface
BUILT_IN_QUESTIONS = [
    "诊断实例i-5ts7cimdfju19tsvl44u eci-t4nd478m1u72re6phsh8 在2025/05/15的宕机原因?",
    "诊断 i-bp131jfclul2mokez67x 在2025-05-21的 重启原因？",
    "对比这2个实例i-5ts7cimdfju19tsvl44u、eci-t4nd478m1u72re6phsh8的对应物理机硬件规格配置差异",
    "查询这个实例i-t4nivj10b4kcfzoy72e6的实例规格信息?",
    "分析ECS实例的性能瓶颈",
    "检查实例的网络连接状态",
    "查看实例的磁盘使用情况",
    "监控实例的CPU和内存使用率"
]

BUILT_IN_QUESTIONS_ZH_CN = [
    "诊断实例i-5ts7cimdfju19tsvl44u eci-t4nd478m1u72re6phsh8 在2025/05/15的宕机原因?",
    "诊断 i-bp131jfclul2mokez67x 在2025-05-21的 重启原因？",
    "对比这2个实例i-5ts7cimdfju19tsvl44u、eci-t4nd478m1u72re6phsh8的对应物理机硬件规格配置差异",
    "查询这个实例i-t4nivj10b4kcfzoy72e6的实例规格信息?",
    "分析ECS实例的性能瓶颈",
    "检查实例的网络连接状态",
    "查看实例的磁盘使用情况",
    "监控实例的CPU和内存使用率"
]
