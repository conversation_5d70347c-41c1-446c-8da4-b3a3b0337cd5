"""
Chat API V1 路由

提供标准化的流式聊天接口，返回统一的API响应格式。
"""

import json
import logging
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse

from deep_diagnose.api.models.chat_request import ChatRequestV1
from deep_diagnose.api.models.chat_response import ChatSessionResponse, ChatMessageResponse
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.services.chat.chat_service import create_chat_service, ChatService
from deep_diagnose.core.events.base_event import ErrorEvent, BaseAgentOutputEvent

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["Chat V1"])


def _is_final_event(agent_event: BaseAgentOutputEvent) -> bool:
    """判断是否为最终事件"""

    # 错误事件是最终事件
    if isinstance(agent_event, ErrorEvent):
        return True

    # 对于有明确结束标记的事件，根据结束标记判断是否结束
    if hasattr(agent_event, "finished"):
        return getattr(agent_event, "finished", False)

    # 对于推理事件，如果有urls内容，认为是最终事件
    if hasattr(agent_event, "urls") and getattr(agent_event, "urls", None):
        urls = getattr(agent_event, "urls")
        if isinstance(urls, list) and len(urls) > 0:
            return True

    # 对于推理事件，如果有result内容且非空，也可能是最终事件
    if hasattr(agent_event, "result") and getattr(agent_event, "result", "").strip():
        return True

    # 对于其他事件类型，默认不是最终事件
    return False


@router.post(
    "/chat",
    summary="V1 流式聊天接口",
    description="""
    V1版本的流式聊天接口，返回标准化的API响应格式。

    **请求格式：**
    ```json
    {
        "question": "用户问题",
        "agent": "智能体类型（如：ReasoningAgent, ReasoningAgentV2）",
        "user_id": "可选，用户ID",
        "session_id": "可选，会话ID",
        "additional_info": "可选，附加信息字典",
        "question_type": "可选，问题类型：input/auto_triggered/manual_triggered，默认为input",
        "ext": "可选，扩展信息字典"
    }
    ```

    **响应格式：** Server-Sent Events (SSE)
    ```json
    {
        "request_id": "请求追踪ID",
        "session_id": "会话ID（新会话时由服务生成）",
        "agent": "智能体类型",
        "data": "智能体输出数据（来自 BaseAgentOutputEvent.to_sse_format()）",
        "status": "处理状态：processing/completed/error"
    }
    ```

    **数据特点：**
    - 每次返回的 data 字段都是智能体的全量累积状态
    - status 字段指示当前处理状态
    - request_id 用于追踪整个请求生命周期
    """,
    responses={
        200: {
            "description": "成功返回事件流",
            "content": {
                "text/plain": {
                    "example": """data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "processing"}

data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "processing"}

data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "completed"}
"""
                }
            },
        },
        400: {"description": "请求参数错误"},
        401: {"description": "未授权"},
        500: {"description": "服务器内部错误"},
    },
)
async def chat_v1(request: ChatRequestV1, user: UserModel = Depends(get_current_user), chat_service: ChatService = Depends(create_chat_service)):
    """
    V1 流式聊天接口

    返回标准化的API响应格式，包装智能体输出数据。

    Args:
        request: 聊天请求
        user: 当前用户
        chat_service: 聊天服务实例

    Returns:
        StreamingResponse: SSE格式的标准API响应数据流
    """
    # 确定用户ID
    user_id = request.user_id or (user.user_id if hasattr(user, "user_id") else "anonymous")

    # 生成请求ID
    request_id = f"req_{uuid4()}"

    logger.info(f"Chat V1 request {request_id} from user {user_id}: {request.question[:100]}...")

    try:

        async def event_generator():
            """事件生成器，返回标准化的API响应格式"""
            actual_session_id = None

            try:
                async for agent_event in chat_service.chat(
                    question=request.question,
                    user_id=user_id if user_id else "anonymous",
                    agent=request.agent,
                    session_id=request.session_id,
                    messages=None,  # V1 API 暂不支持历史消息
                    request_id=request_id,  # 传递request_id用于长时间运行任务
                    thread_id=request_id,
                    question_type=request.question_type.value,  # 传递问题类型（转换为字符串）
                    additional_info=request.additional_info or {},  # 传递附加信息，确保不为None
                ):
                    # 从 chat_service 获取实际的 session_id
                    if actual_session_id is None and hasattr(chat_service, "current_session_id"):
                        actual_session_id = chat_service.current_session_id

                    # 确保是 BaseAgentOutputEvent 类型
                    if isinstance(agent_event, BaseAgentOutputEvent):
                        # 构建标准API响应格式
                        api_response = {
                            "request_id": request_id,
                            "session_id": actual_session_id or request.session_id,
                            "agent": request.agent,
                            "data": agent_event.to_sse_format(),
                            "status": "processing",
                        }
                        # 判断是否为最终状态 - 基于事件类型而不是result内容
                        if hasattr(agent_event, "error") and getattr(agent_event, "error", None):
                            api_response["status"] = "error"
                        elif _is_final_event(agent_event):
                            api_response["status"] = "completed"

                        # 输出SSE格式
                        sse_data = f"data: {json.dumps(api_response, ensure_ascii=False)}\n\n"
                        yield sse_data

                        # 记录关键状态变化
                        logger.debug(f"Request {request_id}: Agent event processed, status: {api_response['status']}")

                    else:
                        # 如果不是预期类型，记录警告并跳过
                        logger.warning(f"Request {request_id}: Unexpected event type: {type(agent_event)}")

            except Exception as e:
                # 发送错误响应
                error_response = {
                    "request_id": request_id,
                    "session_id": actual_session_id or request.session_id,
                    "agent": request.agent,
                    "data": {"error": f"服务内部错误: {str(e)}"},
                    "status": "error",
                }
                error_sse = f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                yield error_sse
                logger.error(f"Request {request_id}: Error in event generator: {e}", exc_info=True)

        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive", "Content-Type": "text/event-stream; charset=utf-8"},
        )

    except Exception as e:
        logger.error(f"Request {request_id}: Chat V1 request failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"聊天服务执行失败: {str(e)}")


@router.get(
    "/chat/sessions",
    summary="获取用户聊天会话列表",
    description="""
    根据user_id查询用户的cloudBotagentSession列表。

    **查询参数：**
    - `user_id`: 必需，用户ID/员工工号

    **响应格式：**
    ```json
    [
        {
            "session_id": "会话ID",
            "title": "会话标题",
            "gmt_create": "创建时间"
        }
    ]
    ```
    """,
    response_model=ChatSessionResponse,
    responses={
        200: {"description": "成功返回会话列表"},
        400: {"description": "请求参数错误"},
        401: {"description": "未授权"},
        500: {"description": "服务器内部错误"},
    },
)
async def get_user_sessions(
    user_id: str = Query(..., description="用户ID/员工工号"),
    user: UserModel = Depends(get_current_user),
    chat_service: ChatService = Depends(create_chat_service),
):
    """
    获取用户聊天会话列表

    Args:
        user_id: 用户ID/员工工号
        user: 当前用户
        chat_service: 聊天服务实例

    Returns:
        ChatSessionResponse: 会话列表
    """
    try:
        # 通过服务层获取用户会话列表
        session_responses = await chat_service.get_user_sessions(user_id=user_id, limit=50)

        logger.info(f"Retrieved {len(session_responses)} sessions for user {user_id}")
        return ChatSessionResponse(total=len(session_responses), sessions=session_responses)

    except Exception as e:
        logger.error(f"Failed to get sessions for user {user_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取用户会话列表失败: {str(e)}")


@router.get(
    "/chat/sessions/{session_id}/messages",
    summary="获取会话消息列表",
    description="""
    根据session_id查询用户的cloudBotagentMessage列表。

    **路径参数：**
    - `session_id`: 必需，会话ID

    **响应格式：**
    ```json
    [
        {
            "message": "消息内容",
            "message_type": "消息类型：human/ai",
            "gmt_create": "创建时间"
        }
    ]
    ```
    **特殊处理：**
    - 如果消息状态为未完成(status=executing)，会从Redis查询最新信息
    """,
    response_model=ChatMessageResponse,
    responses={
        200: {"description": "成功返回消息列表"},
        400: {"description": "请求参数错误"},
        401: {"description": "未授权"},
        404: {"description": "会话不存在"},
        500: {"description": "服务器内部错误"},
    },
)
async def get_session_messages(session_id: str, user: UserModel = Depends(get_current_user), chat_service: ChatService = Depends(create_chat_service)):
    """
    获取会话消息列表

    Args:
        session_id: 会话ID
        user: 当前用户
        chat_service: 聊天服务实例

    Returns:
        ChatMessageResponse: 消息列表
    """
    try:
        # 通过服务层获取会话消息列表（包含Redis实时数据查询）
        message_responses = await chat_service.get_session_messages(session_id=session_id)

        logger.info(f"Retrieved {len(message_responses)} messages for session {session_id}")
        return ChatMessageResponse(total=len(message_responses), messages=message_responses)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get messages for session {session_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取会话消息列表失败: {str(e)}")
