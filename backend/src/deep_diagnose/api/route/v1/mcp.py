"""MCP相关的API路由处理模块

本模块包含与MCP工具功能相关的API路由处理函数。
"""

import logging
from fastapi import APIRouter, Depends, HTTPException

from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.api.models.mcp_response import (
    MCPToolsResponse, MCPRefreshResponse, MCPDescriptionResponse
)
from deep_diagnose.services.mcp.mcp_service import MCPService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/mcp", tags=["mcp"])


@router.get("/tools", response_model=MCPToolsResponse)
async def get_mcp_tools(user: UserModel = Depends(get_current_user)) -> MCPToolsResponse:
    """获取所有MCP工具信息
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        MCPToolsResponse: 包含所有MCP工具信息的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting MCP tools")
        
        mcp_service = MCPService()
        result = await mcp_service.get_tools()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get MCP tools: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_mcp_tools endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/tools/refresh", response_model=MCPRefreshResponse)
async def refresh_mcp_tools(user: UserModel = Depends(get_current_user)) -> MCPRefreshResponse:
    """刷新MCP工具缓存
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        MCPRefreshResponse: 包含刷新结果的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting MCP tools cache refresh")
        
        mcp_service = MCPService()
        result = await mcp_service.refresh_tools()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to refresh MCP tools: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in refresh_mcp_tools endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/tools/description", response_model=MCPDescriptionResponse)
async def get_mcp_tools_description(user: UserModel = Depends(get_current_user)) -> MCPDescriptionResponse:
    """获取MCP工具的Markdown描述
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        MCPDescriptionResponse: 包含工具Markdown描述的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting MCP tools description")
        
        mcp_service = MCPService()
        result = await mcp_service.get_tools_description()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get MCP tools description: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_mcp_tools_description endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )