"""认证相关的API路由处理模块

本模块包含与用户认证相关的API路由处理函数。
"""

import uuid
from urllib import parse as url_parse

from fastapi import APIRouter, Request

from deep_diagnose.security.buc.buc_sso import BucA<PERSON><PERSON>andler
from deep_diagnose.security.buc.models import User
from deep_diagnose.api.models.model import GeneralResponseV1
from src import lifecycle
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.responses import PlainTextResponse
router = APIRouter(tags=["auth"])

IS_ONLINE=False
@router.get("/api/current-user")
async def get_current_user(request: Request):
    """获取当前用户信息

    Args:
        request: HTTP请求对象

    Returns:
        包含用户信息的JSON响应
    """
    user_info = request.session.get("user_info", {})
    return JSONResponse(user_info)


@router.get("/sendBucSSOToken.do")
async def handle_buc_auth(request: Request):
    """处理BUC认证请求

    Args:
        request: HTTP请求对象

    Returns:
        重定向响应，成功则重定向到请求的返回URL，失败则重定向到BUC登录页
    """
    sso_token = request.query_params.get("SSO_TOKEN")
    back_url = request.query_params.get("BACK_URL")

    user_info = BucAuthHandler().get_authenticated_user(sso_token)
    if user_info["id"] is not None:
        user = User(user_info["id"])
        # 保存用户信息到会话
        request.session["user_info"] = user_info
        return RedirectResponse(back_url or "index")
    else:
        back_url = url_parse.urljoin(request.base_url, request.query_params.get("next"))
        # 重定向到BUC登录页
        return BucAuthHandler().authenticate_redirect(back_url)


@router.get("/public/buc/getUser")
def get_user(request: Request):
    """获取用户信息

    Args:
        request: HTTP请求对象

    Returns:
        包含用户信息的通用响应对象
    """
    request_uuid = str(uuid.uuid1())
    data = GeneralResponseV1(
        status=200,
        request_uuid=request_uuid,
        data=request.session.get("user_info"),
    )
    return data


@router.get("/login")
def login(request: Request):
    """处理登录请求

    Args:
        request: HTTP请求对象

    Returns:
        重定向响应，跳转至BUC登录页
    """
    # 保存back_url用于登录后的重定向
    back_url = url_parse.urljoin(request.base_url, request.query_params.get("next"))
    # 返回重定向响应，跳转至BUC登录页
    return BucAuthHandler().authenticate_redirect(back_url)


@router.get("/status.taobao")
async def status_taobao(request: Request):
    """处理status.taobao请求，用于应用生命周期管理

    如果check返回None代表第一次请求，调用online函数，返回online文件内容
    如果check返回online文件内容，说明已经上线

    Args:
        request: HTTP请求对象

    Returns:
        PlainTextResponse响应对象
    """
    global IS_ONLINE
    result = lifecycle.check()

    if not result and not IS_ONLINE:
        lifecycle.online()
        IS_ONLINE = True
        return PlainTextResponse(lifecycle.check() or "")
    else:
        if result:
            return PlainTextResponse(result)
        else:
            return PlainTextResponse("not found", status_code=404)

