"""聊天相关的API路由处理模块

本模块包含与聊天功能相关的API路由处理函数。
业务逻辑已迁移到 chat_service_deprecated.py 中，此模块专注于HTTP请求处理。
"""

import logging
from typing import Optional
from uuid import uuid4

from fastapi import APIRouter, Depends, Query, HTTPException
from fastapi.responses import StreamingResponse

from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.api.models.chat_request import ChatRequest
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.services.chat.chat_service_deprecated import create_chat_service
from deep_diagnose.domain.chat.repository import chat_repository
from deep_diagnose.domain.chat.schemas import (
    ChatSessionHistoryResponse, 
    ChatSessionHistory,
    ChatMessagesResponse,
    ChatMessageHistory
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/chat", tags=["chat"])


@router.post("/stream")
async def chat_stream(request: ChatRequest, user: UserModel = Depends(get_current_user)):
    """处理聊天流请求
    
    要求携带有效 JWT Token。业务逻辑委托给 ChatService 处理。
    
    Args:
        request: 聊天请求对象
        user: 当前用户信息

    Returns:
        流式响应对象
    """
    try:
        # 创建聊天服务实例
        chat_service = create_chat_service()

        # 从messages中提取问题
        messages = request.model_dump()["messages"]
        question = ""
        if messages:
            # 找到最后一个用户消息作为问题
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    question = msg.get("content", "")
                    break
        
        # 调用聊天服务生成流式响应
        return StreamingResponse(
            chat_service.chat(
                question=question,
                user_id=user.user_id if hasattr(user, 'user_id') else "anonymous",
                session_id=None,
                messages=messages
            ),
            media_type="text/event-stream",
        )
    
    except Exception as e:
        logger.error(f"Error in chat_stream endpoint: {e}")
        # 重新抛出异常，让FastAPI的异常处理器处理
        raise
