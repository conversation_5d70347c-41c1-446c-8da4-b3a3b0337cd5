"""
日志中间件模块 - 提供请求和响应日志记录功能

本模块负责记录所有 /message 请求和响应的详细信息，用于调试和审计目的。
"""
import base64
import datetime
import json
import logging
import re
from typing import Dict, Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, _StreamingResponse
from starlette.responses import StreamingResponse


class LogMiddleware(BaseHTTPMiddleware):
    """日志中间件，用于记录所有 /message 和 /sse 请求和响应信息"""

    # 使用专用的日志记录器
    logger: logging.Logger = logging.getLogger("request_detail")

    # 需要记录的路径前缀
    MONITORED_PATHS = ["/messages/", "/sse", "/mcp"]

    async def dispatch(self, request: Request, call_next):
        """处理请求并记录日志

        如果请求路径包含监控路径前缀，则记录请求和响应信息。
        否则，直接传递给下一个处理函数。

        Args:
            request: FastAPI请求对象
            call_next: 下一个中间件处理函数

        Returns:
            响应对象
        """
        # 检查是否需要记录此请求
        if not self._should_log_request(request):
            return await call_next(request)

        # 创建日志上下文
        log_context = self._create_log_context(request)

        # 获取请求详细信息
        await self._add_request_details(request, log_context)

        try:
            # 调用下一个处理函数获取响应
            response = await call_next(request)

            # 记录响应状态码
            log_context["status_code"] = response.status_code

            # 处理不同类型的响应
            if isinstance(response, StreamingResponse) or isinstance(response, _StreamingResponse):
                return await self._handle_streaming_response(response, log_context)
            else:
                # 处理普通响应
                self._log_regular_response(response, log_context)
                return response

        except Exception as e:
            # 记录异常信息
            log_context["error"] = str(e)
            log_context["status_code"] = 500
            self._log_info(**log_context)
            raise

    def _should_log_request(self, request: Request) -> bool:
        """判断是否应该记录此请求

        Args:
            request: 请求对象

        Returns:
            如果请求路径包含监控路径前缀，则返回True
        """
        return any(path in request.url.path for path in self.MONITORED_PATHS)

    def _create_log_context(self, request: Request) -> Dict[str, Any]:
        """创建基本日志上下文

        Args:
            request: 请求对象

        Returns:
            包含基本请求信息的字典
        """
        return {
            "request_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
            "request_method": request.method,
            "request_url": request.url.path
        }

    async def _add_request_details(self, request: Request, log_context: Dict[str, Any]) -> None:
        """添加请求详细信息到日志上下文

        Args:
            request: 请求对象
            log_context: 日志上下文字典
        """
        try:
            query_params = dict(request.query_params)

            if request.method == "POST":
                # 处理POST请求
                body = await request.body()
                log_context.update({
                    "request_body": body.decode(),
                    "session_id": query_params.get("session_id")
                })
            elif request.method == "GET" and "token" in query_params:
                # 处理带有token的GET请求
                try:
                    decoded_token = base64.b64decode(query_params.get("token")).decode('utf-8')
                    # 提取 $ 分隔符后面的内容作为用户信息
                    user_info = decoded_token.split('$', 1)[1] if '$' in decoded_token else decoded_token
                    log_context.update({"request_user": user_info})
                except Exception as e:
                    log_context.update({"token_error": str(e)})
        except Exception as e:
            logging.error(f"Error processing request details: {e}")
            log_context.update({"request_error": str(e)})

    def _log_regular_response(self, response: Any, log_context: Dict[str, Any]) -> None:
        """记录普通响应信息

        Args:
            response: 响应对象
            log_context: 日志上下文
        """
        log_context["response_body"] = response
        self._log_info(**log_context)

    async def _handle_streaming_response(
            self,
            response: StreamingResponse | _StreamingResponse,
            log_context: Dict[str, Any]
    ) -> StreamingResponse:
        """处理流式响应并记录日志

        Args:
            response: 流式响应对象
            log_context: 日志上下文

        Returns:
            包装了日志记录功能的流式响应对象
        """
        async def stream_with_logging():
            """包装流式响应迭代器，添加日志记录功能"""
            async for chunk in response.body_iterator:
                try:
                    # 解码响应块
                    decoded_chunk = chunk.decode('utf-8')
                    print(decoded_chunk)
                    # 创建日志上下文副本
                    chunk_log_context = log_context.copy()

                    # 检查是否包含 "event: endpoint" 和 session_id
                    if "event: endpoint" in decoded_chunk:
                        # 尝试提取 session_id
                        session_match = re.search(r'session_id=([a-f0-9]+)', decoded_chunk)
                        if session_match:
                            session_id = session_match.group(1)
                            # 将 session_id 添加到日志上下文中
                            chunk_log_context["session_id"] = session_id

                    # 记录响应内容（去除前后空白字符）
                    chunk_log_context["response_body"] = decoded_chunk.strip()
                    self._log_info(**chunk_log_context)

                    # 传递原始响应块
                    yield chunk
                except Exception as e:
                    # 记录处理响应块时的错误
                    error_context = log_context.copy()
                    error_context["chunk_error"] = str(e)
                    self._log_info(**error_context)
                    yield chunk  # 仍然传递原始块

        # 创建新的流式响应
        return StreamingResponse(
            stream_with_logging(),
            media_type=response.media_type,
            headers=response.headers,
        )

    def _log_info(self, **kwargs) -> None:
        """记录INFO级别的日志

        将日志上下文序列化为JSON并记录

        Args:
            **kwargs: 日志上下文键值对
        """
        try:
            self.logger.info(json.dumps(kwargs, ensure_ascii=False))
        except Exception as e:
            self.logger.error(f"Error logging info: {e}")
