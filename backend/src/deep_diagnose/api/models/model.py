from typing import Optional, List, Union
from enum import Enum
from datetime import datetime

from pydantic import BaseModel, Field


class UserModel(BaseModel):
    access_key: Optional[str] = ""
    user_id: Optional[str] = ""
    user_name: Optional[str] = ""
    user_type: Optional[str] = ""
    pop_user: Optional[str] = ""


class GeneralResponseV1(BaseModel):
    status: Optional[int] = None
    request_uuid: Optional[str] = None
    data: Union[str, List[str], List[dict], dict, int] = ""
    error_message: Union[List[str], str, None, dict] = ""
