from typing import List, Optional, Union

from pydantic import BaseModel, Field
from deep_diagnose.domain.chat.models import MessageType


class ContentItem(BaseModel):
    type: str = Field(..., description="The type of content (text, image, etc.)")
    text: Optional[str] = Field(None, description="The text content if type is 'text'")
    image_url: Optional[str] = Field(None, description="The image URL if type is 'image'")


class ChatMessage(BaseModel):
    role: str = Field(..., description="The role of the message sender (user or assistant)")
    content: Union[str, List[ContentItem]] = Field(
        ...,
        description="The content of the message, either a string or a list of content items",
    )


class ChatRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field([], description="History of messages between the user and the assistant")
    debug: Optional[bool] = Field(False, description="Whether to enable debug logging")
    thread_id: Optional[str] = Field("__default__", description="A specific conversation identifier")
    max_plan_iterations: Optional[int] = Field(1, description="The maximum number of plan iterations")
    max_step_num: Optional[int] = Field(3, description="The maximum number of steps in a plan")
    max_search_results: Optional[int] = Field(3, description="The maximum number of search results")
    auto_accepted_plan: Optional[bool] = Field(False, description="Whether to automatically accept the plan")
    interrupt_feedback: Optional[str] = Field(None, description="Interrupt feedback from the user on the plan")
    mcp_settings: Optional[dict] = Field(None, description="MCP settings for the chat request")
    enable_background_investigation: Optional[bool] = Field(True, description="Whether to get background investigation before plan")


class TTSRequest(BaseModel):
    text: str = Field(..., description="The text to convert to speech")
    voice_type: Optional[str] = Field("BV700_V2_streaming", description="The voice type to use")
    encoding: Optional[str] = Field("mp3", description="The audio encoding format")
    speed_ratio: Optional[float] = Field(1.0, description="Speech speed ratio")
    volume_ratio: Optional[float] = Field(1.0, description="Speech volume ratio")
    pitch_ratio: Optional[float] = Field(1.0, description="Speech pitch ratio")
    text_type: Optional[str] = Field("plain", description="Text type (plain or ssml)")
    with_frontend: Optional[int] = Field(1, description="Whether to use frontend processing")
    frontend_type: Optional[str] = Field("unitTson", description="Frontend type")


class GeneratePodcastRequest(BaseModel):
    content: str = Field(..., description="The content of the podcast")


class GeneratePPTRequest(BaseModel):
    content: str = Field(..., description="The content of the ppt")


class GenerateProseRequest(BaseModel):
    prompt: str = Field(..., description="The content of the prose")
    option: str = Field(..., description="The option of the prose writer")
    command: Optional[str] = Field("", description="The user custom command of the prose writer")


class ChatRequestV1(BaseModel):
    """V1 聊天请求模型"""

    question: str = Field(..., description="用户问题", min_length=1, max_length=10000)
    agent: str = Field("ReasoningAgent", description="智能体类型")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则从认证信息获取")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空则创建新会话")
    additional_info: Optional[dict] = Field(None, description="附加信息，用于传递额外的上下文数据")
    question_type: MessageType = Field(MessageType.HUMAN_QUERY, description="问题类型：input(用户输入)/auto_triggered(自动触发)/manual_triggered(手动触发)")
