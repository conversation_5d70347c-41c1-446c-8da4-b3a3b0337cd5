"""
MCP API 响应模型

定义MCP相关API的请求和响应数据模型
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class MCPToolInfo(BaseModel):
    """MCP工具信息模型"""

    name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具描述")
    args_schema: Optional[Dict[str, Any]] = Field(None, description="工具参数schema")


class MCPServerInfo(BaseModel):
    """MCP服务器信息模型"""

    tools: List[MCPToolInfo] = Field(..., description="服务器工具列表")
    tool_count: int = Field(..., description="工具数量")


class MCPToolsData(BaseModel):
    """MCP工具数据模型"""

    servers: Dict[str, MCPServerInfo] = Field(..., description="服务器信息映射")
    total_servers: int = Field(..., description="总服务器数量")
    total_tools: int = Field(..., description="总工具数量")


class MCPToolsResponse(BaseModel):
    """获取MCP工具响应模型"""

    success: bool = Field(..., description="操作是否成功")
    data: Optional[MCPToolsData] = Field(None, description="工具数据")
    error: Optional[str] = Field(None, description="错误信息")


class MCPRefreshData(BaseModel):
    """MCP刷新数据模型"""

    total_servers: int = Field(..., description="总服务器数量")
    total_tools: int = Field(..., description="总工具数量")
    servers: Dict[str, int] = Field(..., description="各服务器工具数量映射")


class MCPRefreshResponse(BaseModel):
    """刷新MCP工具响应模型"""

    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    data: Optional[MCPRefreshData] = Field(None, description="刷新结果数据")
    error: Optional[str] = Field(None, description="错误信息")


class MCPDescriptionData(BaseModel):
    """MCP工具描述数据模型"""

    description: str = Field(..., description="工具描述内容")
    format: str = Field(default="markdown", description="描述格式")


class MCPDescriptionResponse(BaseModel):
    """获取MCP工具描述响应模型"""

    success: bool = Field(..., description="操作是否成功")
    data: Optional[MCPDescriptionData] = Field(None, description="描述数据")
    error: Optional[str] = Field(None, description="错误信息")
