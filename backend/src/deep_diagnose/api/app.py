import logging
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

from logging_config import configure_logging
from deep_diagnose.api.middleware import AuthMiddleware
from deep_diagnose.common.config import get_config
from deep_diagnose.data.database import db_manager
from deep_diagnose.api.route.v1 import mcp, tasks, chat as chat_v1
from deep_diagnose.api.route import chat, token, auth

# Configure logging
configure_logging()

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI 应用生命周期管理"""
    # 启动时初始化数据库
    logger.info("🚀 Starting FastAPI application...")
    try:
        await db_manager.init_database()
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize database: {e}")
        raise
    
    yield
    
    # 关闭时清理数据库连接
    logger.info("🛑 Shutting down FastAPI application...")
    try:
        await db_manager.close_database()
        logger.info("✅ Database connections closed successfully")
    except Exception as e:
        logger.error(f"❌ Failed to close database connections: {e}")


app = FastAPI(
    title="ECS Deep Diagnose API",
    description="API for Deer",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.add_middleware(AuthMiddleware)
app.add_middleware(SessionMiddleware, secret_key=get_config().app.secret)  # 请确保这是一个安全的密钥，最好从配置中读取

# 导入路由模块

# 注册路由
app.include_router(auth.router)
app.include_router(chat.router)
app.include_router(token.router)
app.include_router(tasks.router)
app.include_router(mcp.router)

# 注册 V2 路由
app.include_router(chat_v1.router)
