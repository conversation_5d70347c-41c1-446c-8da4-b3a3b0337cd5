#!/usr/bin/env python3

import argparse
import logging
import signal
import sys
import uvicorn
from logging_config import configure_logging

# Configure logging at the module level so it's available immediately.
configure_logging()
logger = logging.getLogger(__name__)


def parse_arguments() -> argparse.Namespace:
    """
    Parses command-line arguments for the server configuration.

    Returns:
        argparse.Namespace: An object containing the parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Run the ECS Deep Diagnose API server")

    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development (default: False)",
    )
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level for Uvicorn (default: info)",
    )

    return parser.parse_args()


def setup_signal_handlers() -> None:
    """
    Registers signal handlers for graceful shutdown on SIGTERM and SIGINT.
    """

    def handle_shutdown(signum, frame):
        """
        Logs the shutdown signal and exits gracefully.
        """
        logger.info(f"Received signal {signum}. Starting graceful shutdown...")
        sys.exit(0)

    signal.signal(signal.SIGTERM, handle_shutdown)
    signal.signal(signal.SIGINT, handle_shutdown)
    logger.debug("Signal handlers for SIGTERM and SIGINT registered.")


def main() -> None:
    """
    Main function to configure and run the API server.

    This function orchestrates parsing arguments, setting up signals,
    and starting the Uvicorn server.
    """
    # 1. Parse command line arguments
    args = parse_arguments()

    # 2. Set up graceful shutdown
    setup_signal_handlers()

    # 3. Run the server
    try:
        logger.info(f"Starting ECS Deep Diagnose API server on {args.host}:{args.port}")
        logger.info(f"Auto-reload: {'Enabled' if args.reload else 'Disabled'}")
        
        # Test import before starting server
        logger.info("Testing application import...")
        try:
            import deep_diagnose.api.app
            logger.info("Application import successful")
        except Exception as import_error:
            logger.error(f"Application import failed: {import_error}")
            logger.error("Full traceback:", exc_info=True)
            print(f"ERROR: Application import failed: {import_error}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

        logger.info("Starting Uvicorn server...")
        uvicorn.run(
            "deep_diagnose.api.app:app",  # The application to run
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level,
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        logger.error("Full traceback:", exc_info=True)
        print(f"ERROR: Failed to start server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()