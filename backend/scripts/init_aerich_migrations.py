#!/usr/bin/env python3
"""
初始化Aerich迁移系统的脚本
用于设置数据库迁移管理
"""
import asyncio
import sys
import os
import subprocess

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {cmd}")
    
    try:
        # 获取当前脚本所在目录的父目录（backend目录）
        backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=backend_dir)
        
        if result.returncode == 0:
            print(f"✓ {description}成功")
            if result.stdout:
                print("输出:", result.stdout)
        else:
            print(f"✗ {description}失败")
            if result.stderr:
                print("错误:", result.stderr)
            if result.stdout:
                print("输出:", result.stdout)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"✗ 执行命令失败: {e}")
        return False


async def setup_aerich():
    """设置Aerich迁移系统"""
    print("=== 设置Aerich数据库迁移系统 ===")
    
    # 1. 检查aerich是否安装
    print("\n1. 检查aerich安装状态...")
    if not run_command("python -m aerich --help", "检查aerich"):
        print("请先安装aerich: pip install aerich")
        return False
    
    # 2. 初始化aerich配置
    print("\n2. 初始化aerich配置...")
    aerich_config = """
[tool.aerich]
tortoise_orm = "deep_diagnose.data.database.TORTOISE_ORM"
location = "./migrations"
src_folder = "./src"
"""
    
    # 检查pyproject.toml是否已有aerich配置
    backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    pyproject_path = os.path.join(backend_dir, "pyproject.toml")
    try:
        with open(pyproject_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '[tool.aerich]' not in content:
            with open(pyproject_path, 'a', encoding='utf-8') as f:
                f.write(aerich_config)
            print("✓ 已添加aerich配置到pyproject.toml")
        else:
            print("✓ aerich配置已存在")
    except Exception as e:
        print(f"✗ 配置aerich失败: {e}")
        return False
    
    # 3. 初始化迁移
    print("\n3. 初始化迁移...")
    if not run_command("python -m aerich init-db", "初始化迁移数据库"):
        print("初始化失败，可能是因为:")
        print("- 数据库连接配置错误")
        print("- 模型定义有问题")
        print("- 已经初始化过")
    
    return True


async def create_migration_for_user_id():
    """为user_id字段创建迁移"""
    print("\n=== 创建user_id字段迁移 ===")
    
    # 1. 生成迁移文件
    print("\n1. 生成迁移文件...")
    if run_command("python -m aerich migrate --name fix_user_id_type", "生成迁移"):
        print("✓ 迁移文件已生成")
    else:
        print("✗ 生成迁移失败")
        return False
    
    # 2. 应用迁移
    print("\n2. 应用迁移...")
    if run_command("python -m aerich upgrade", "应用迁移"):
        print("✓ 迁移已应用")
        return True
    else:
        print("✗ 应用迁移失败")
        return False


async def main():
    """主函数"""
    print("=== Aerich数据库迁移设置工具 ===")
    print()
    print("此工具将帮助您:")
    print("1. 设置Aerich迁移系统")
    print("2. 创建user_id字段类型修复的迁移")
    print("3. 应用迁移到数据库")
    print()
    
    try:
        # 设置aerich
        if not await setup_aerich():
            print("\n❌ Aerich设置失败")
            return
        
        # 询问是否创建user_id迁移
        response = input("\n是否创建user_id字段类型修复的迁移? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            await create_migration_for_user_id()
        
        print("\n=== 使用说明 ===")
        print("1. 修改模型后生成迁移:")
        print("   cd backend && python -m aerich migrate --name your_migration_name")
        print()
        print("2. 应用迁移:")
        print("   cd backend && python -m aerich upgrade")
        print()
        print("3. 查看迁移历史:")
        print("   cd backend && python -m aerich history")
        print()
        print("4. 回滚迁移:")
        print("   cd backend && python -m aerich downgrade")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())