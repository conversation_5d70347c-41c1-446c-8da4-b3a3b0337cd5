#!/usr/bin/env python3
"""
数据库管理器 - 极简版
只做两件事：1) 数据表新建、更新 2) 维护几个用户
"""
import sys
import asyncio
from pathlib import Path
import argparse
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from deep_diagnose.data.database import init_db, close_db
from deep_diagnose.domain.user.models import CloudbotAgentUser
from deep_diagnose.domain.user.repository import user_crud

# 暂时注释掉聊天相关的导入，专注于用户管理
# from deep_diagnose.domain.chat.repository import chat_session_crud, chat_message_crud

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 默认用户配置 - 直接在代码中维护，简单明了
DEFAULT_USERS = {
    "admin": "admin",
    "user": "user",
    "viewer": "viewer"

}


async def init_database():
    """初始化数据库表"""
    try:
        logger.info("=== 初始化数据库表 ===")
        await init_db()
        logger.info("✅ 数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        return False


async def setup_users():
    """设置默认用户"""
    try:
        logger.info("=== 设置用户 ===")
        success_count = 0

        for username, password in DEFAULT_USERS.items():
            # 检查用户是否存在
            existing_user = await CloudbotAgentUser.get_or_none(username=username)

            if existing_user:
                # 更新密码
                existing_user.password = password
                await existing_user.save()
                logger.info(f"✅ 更新用户: {username}")
            else:
                # 创建新用户
                await CloudbotAgentUser.create(username=username, password=password)
                logger.info(f"✅ 创建用户: {username}")

            success_count += 1

        logger.info(f"✅ 用户设置完成: {success_count}/{len(DEFAULT_USERS)}")
        return True

    except Exception as e:
        logger.error(f"❌ 用户设置失败: {e}")
        return False


async def show_status():
    """显示数据库状态"""
    try:
        logger.info("=== 数据库状态 ===")

        # 统计信息
        user_count = await user_crud.count()

        logger.info(f"用户数量: {user_count}")
        # 暂时注释掉聊天统计，专注于用户管理
        # session_count = await chat_session_crud.count()
        # message_count = await chat_message_crud.count()
        # logger.info(f"会话数量: {session_count}")
        # logger.info(f"消息数量: {message_count}")

        # 用户列表
        logger.info("\n=== 用户列表 ===")
        users = await CloudbotAgentUser.all()
        for user in users:
            logger.info(f"ID: {user.id} | 用户名: {user.username} | 密码: {user.password}")

        return True

    except Exception as e:
        logger.error(f"❌ 获取状态失败: {e}")
        return False


async def verify_user(username: str, password: str):
    """验证用户登录"""
    try:
        user = await CloudbotAgentUser.get_or_none(username=username)
        if user and user.password == password:
            logger.info(f"✅ 用户 '{username}' 验证成功")
            return True
        else:
            logger.error(f"❌ 用户 '{username}' 验证失败")
            return False
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False


async def add_user(username: str, password: str):
    """添加新用户"""
    try:
        existing_user = await CloudbotAgentUser.get_or_none(username=username)
        if existing_user:
            logger.error(f"❌ 用户 '{username}' 已存在")
            return False

        await CloudbotAgentUser.create(username=username, password=password)
        logger.info(f"✅ 添加用户成功: {username}")
        return True

    except Exception as e:
        logger.error(f"❌ 添加用户失败: {e}")
        return False


async def update_password(username: str, new_password: str):
    """更新用户密码"""
    try:
        user = await CloudbotAgentUser.get_or_none(username=username)
        if not user:
            logger.error(f"❌ 用户 '{username}' 不存在")
            return False

        user.password = new_password
        await user.save()
        logger.info(f"✅ 用户 '{username}' 密码更新成功")
        return True

    except Exception as e:
        logger.error(f"❌ 密码更新失败: {e}")
        return False


async def clear_all_users():
    """清理所有用户"""
    try:
        logger.info("=== 清理所有用户 ===")
        users = await CloudbotAgentUser.all()
        user_count = len(users)

        if user_count == 0:
            logger.info("✅ 没有用户需要清理")
            return True

        # 删除所有用户
        await CloudbotAgentUser.all().delete()
        logger.info(f"✅ 已清理 {user_count} 个用户")
        return True

    except Exception as e:
        logger.error(f"❌ 清理用户失败: {e}")
        return False


async def reset_to_defaults():
    """重置为默认用户"""
    try:
        logger.info("=== 重置为默认用户 ===")

        # 先清理所有用户
        await clear_all_users()

        # 再设置默认用户
        await setup_users()

        logger.info("✅ 重置为默认用户完成")
        return True

    except Exception as e:
        logger.error(f"❌ 重置失败: {e}")
        return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库管理器 - 极简版")
    parser.add_argument("action", nargs="?", choices=[
        "init", "setup", "status", "verify", "add", "update", "clear", "reset"
    ], help="操作类型")
    parser.add_argument("--username", "-u", help="用户名")
    parser.add_argument("--password", "-p", help="密码")
    parser.add_argument("--new-password", "-np", help="新密码")

    args = parser.parse_args()

    try:
        await init_db()

        if args.action == "init":
            # 完整初始化：创建表 + 设置用户
            success1 = await init_database()
            success2 = await setup_users()
            if success1 and success2:
                logger.info("🎉 数据库完整初始化成功!")

        elif args.action == "setup":
            # 只设置用户
            await setup_users()

        elif args.action == "status":
            # 显示状态
            await show_status()

        elif args.action == "verify":
            # 验证用户
            if not args.username or not args.password:
                logger.error("验证用户需要 --username 和 --password")
                return
            await verify_user(args.username, args.password)

        elif args.action == "add":
            # 添加用户
            if not args.username or not args.password:
                logger.error("添加用户需要 --username 和 --password")
                return
            await add_user(args.username, args.password)

        elif args.action == "update":
            # 更新密码
            if not args.username or not args.new_password:
                logger.error("更新密码需要 --username 和 --new-password")
                return
            await update_password(args.username, args.new_password)

        elif args.action == "clear":
            # 清理所有用户
            await clear_all_users()

        elif args.action == "reset":
            # 重置为默认用户
            await reset_to_defaults()

        else:
            # 显示帮助
            logger.info("数据库管理器 - 极简版")
            logger.info("")
            logger.info("主要功能:")
            logger.info("  python db_manager.py init                    # 完整初始化(表+用户)")
            logger.info("  python db_manager.py setup                  # 设置默认用户")
            logger.info("  python db_manager.py status                 # 查看状态")
            logger.info("")
            logger.info("用户管理:")
            logger.info("  python db_manager.py verify -u admin -p admin")
            logger.info("  python db_manager.py add -u admin -p admin")
            logger.info("  python db_manager.py update -u admin -np admin")
            logger.info("")
            logger.info("清理功能:")
            logger.info("  python db_manager.py clear                   # 清理所有用户")
            logger.info("  python db_manager.py reset                   # 重置为默认用户")
            logger.info("")
            logger.info(f"默认用户: {', '.join(DEFAULT_USERS.keys())}")

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"❌ 操作失败: {e}")
        sys.exit(1)
    finally:
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())