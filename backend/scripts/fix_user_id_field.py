#!/usr/bin/env python3
"""
修复user_id字段类型的脚本
将CloudbotAgentChatSession表中的user_id字段从VARCHAR改为INT
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from deep_diagnose.data.database import init_db, close_db
from tortoise import Tortoise
from tortoise.backends.mysql.client import MySQLClient


async def check_user_id_field_type():
    """检查user_id字段的当前类型"""
    try:
        # 获取数据库连接
        db = Tortoise.get_connection("default")
        
        # 查询字段信息
        query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'cloudbotAgentChatSession' 
        AND COLUMN_NAME = 'user_id'
        """
        
        result = await db.execute_query_dict(query)
        
        if result:
            field_info = result[0]
            print(f"当前user_id字段信息:")
            print(f"  字段名: {field_info['COLUMN_NAME']}")
            print(f"  数据类型: {field_info['DATA_TYPE']}")
            print(f"  允许NULL: {field_info['IS_NULLABLE']}")
            print(f"  默认值: {field_info['COLUMN_DEFAULT']}")
            return field_info['DATA_TYPE'].upper()
        else:
            print("未找到user_id字段")
            return None
            
    except Exception as e:
        print(f"检查字段类型失败: {e}")
        return None


async def fix_user_id_field_type():
    """修复user_id字段类型"""
    try:
        # 获取数据库连接
        db = Tortoise.get_connection("default")
        
        print("开始修复user_id字段类型...")
        
        # 1. 检查当前字段类型
        current_type = await check_user_id_field_type()
        if not current_type:
            print("无法获取当前字段类型，退出")
            return False
        
        if current_type == 'INT':
            print("user_id字段已经是INT类型，无需修复")
            return True
        
        # 2. 备份表（可选）
        print("建议在修改前备份数据...")
        
        # 3. 更新字段类型
        print(f"将user_id字段从{current_type}改为INT...")
        
        # 先将非数字字符串转换为数字
        update_query = """
        UPDATE cloudbotAgentChatSession 
        SET user_id = CASE 
            WHEN user_id REGEXP '^[0-9]+$' THEN CAST(user_id AS UNSIGNED)
            ELSE ABS(CRC32(user_id)) % 1000000000
        END
        WHERE user_id IS NOT NULL
        """
        
        await db.execute_query(update_query)
        print("✓ 数据转换完成")
        
        # 修改字段类型
        alter_query = """
        ALTER TABLE cloudbotAgentChatSession 
        MODIFY COLUMN user_id INT DEFAULT 0
        """
        
        await db.execute_query(alter_query)
        print("✓ 字段类型修改完成")
        
        # 4. 验证修改结果
        new_type = await check_user_id_field_type()
        if new_type == 'INT':
            print("✅ user_id字段类型修复成功！")
            return True
        else:
            print(f"❌ 修复失败，当前类型仍为: {new_type}")
            return False
            
    except Exception as e:
        print(f"修复字段类型失败: {e}")
        return False


async def main():
    """主函数"""
    print("=== CloudbotAgentChatSession.user_id字段类型修复工具 ===")
    print()
    
    try:
        # 初始化数据库连接
        print("1. 初始化数据库连接...")
        await init_db()
        print("✓ 数据库连接成功")
        
        # 检查当前字段类型
        print("\n2. 检查当前字段类型...")
        current_type = await check_user_id_field_type()
        
        if current_type == 'INT':
            print("✅ user_id字段已经是正确的INT类型")
        else:
            print(f"\n⚠️  user_id字段当前类型为: {current_type}")
            print("需要修复为INT类型")
            
            # 询问是否继续
            response = input("\n是否继续修复? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("\n3. 开始修复...")
                success = await fix_user_id_field_type()
                
                if success:
                    print("\n🎉 修复完成！")
                    print("现在可以正常使用ChatService创建会话了")
                else:
                    print("\n❌ 修复失败，请检查错误信息")
            else:
                print("取消修复")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await close_db()
        print("\n数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())