#!/usr/bin/env python3
"""
Parse langgraph_event.txt file and extract formatted event data
"""

import re
import json
import ast
from typing import Dict, Any, List, Optional


def safe_eval_dict(text: str) -> Optional[Dict[str, Any]]:
    """Safely parse dictionary string"""
    try:
        # Try using ast.literal_eval
        return ast.literal_eval(text)
    except:
        try:
            # Try using eval (unsafe, but can be used for known data sources)
            return eval(text)
        except:
            return None


def extract_aimessagechunk_data(event_data_str: str) -> Optional[Dict[str, Any]]:
    """Extract AIMessageChunk information from event data string"""
    try:
        # Find the start position of AIMessageChunk
        aimessage_match = re.search(r'AIMessageChunk\((.*?)\)', event_data_str, re.DOTALL)
        if not aimessage_match:
            return None
        
        aimessage_content = aimessage_match.group(1)
        
        # Parse various fields of AIMessageChunk
        result = {}
        
        # Extract content
        content_match = re.search(r"content='([^']*)'", aimessage_content)
        if content_match:
            result['content'] = content_match.group(1)
        
        # Extract additional_kwargs
        additional_kwargs_match = re.search(r"additional_kwargs=(\{.*?\})", aimessage_content)
        if additional_kwargs_match:
            kwargs_str = additional_kwargs_match.group(1)
            try:
                result['additional_kwargs'] = safe_eval_dict(kwargs_str)
            except:
                result['additional_kwargs'] = kwargs_str
        
        # Extract response_metadata
        response_metadata_match = re.search(r"response_metadata=(\{.*?\})", aimessage_content)
        if response_metadata_match:
            metadata_str = response_metadata_match.group(1)
            try:
                result['response_metadata'] = safe_eval_dict(metadata_str)
            except:
                result['response_metadata'] = metadata_str
        
        # Extract id
        id_match = re.search(r"id='([^']*)'", aimessage_content)
        if id_match:
            result['id'] = id_match.group(1)
        
        # Extract tool_calls
        tool_calls_match = re.search(r"tool_calls=(\[.*?\])", aimessage_content)
        if tool_calls_match:
            tool_calls_str = tool_calls_match.group(1)
            try:
                result['tool_calls'] = safe_eval_dict(tool_calls_str)
            except:
                result['tool_calls'] = tool_calls_str
        
        # Extract tool_call_chunks
        tool_call_chunks_match = re.search(r"tool_call_chunks=(\[.*?\])", aimessage_content)
        if tool_call_chunks_match:
            chunks_str = tool_call_chunks_match.group(1)
            try:
                result['tool_call_chunks'] = safe_eval_dict(chunks_str)
            except:
                result['tool_call_chunks'] = chunks_str
        
        return result
        
    except Exception as e:
        print(f"Error parsing AIMessageChunk: {e}")
        return None


def extract_metadata(event_data_str: str) -> Optional[Dict[str, Any]]:
    """Extract metadata from event data string"""
    try:
        # Find metadata part (usually in second parameter position)
        # Look for dictionary after AIMessageChunk
        parts = event_data_str.split('), {')
        if len(parts) >= 2:
            metadata_str = '{' + parts[1]
            # Remove trailing extra parentheses
            metadata_str = re.sub(r'\)+$', '', metadata_str)
            
            # Extract langgraph_checkpoint_ns
            langgraph_id_match = re.search(r"'langgraph_checkpoint_ns': '([^']*)'", metadata_str)
            if langgraph_id_match:
                return {
                    'langgraph_checkpoint_ns': langgraph_id_match.group(1),
                    'raw_metadata': metadata_str
                }
        
        return None
    except Exception as e:
        print(f"Error parsing metadata: {e}")
        return None


def parse_langgraph_events(file_path: str) -> List[Dict[str, Any]]:
    """Parse langgraph_event.txt file"""
    events = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Use regex to match each event
    event_pattern = r'Event #(\d+) - ([\d\-T:\.]+)\n-+\nAgent: \((.*?)\)\nXXXX: \'(.*?)\'\nEvent Data: (.*?)(?=\n\nEvent #|\n\n$|$)'
    
    matches = re.findall(event_pattern, content, re.DOTALL)
    
    for match in matches:
        event_num, timestamp, agent, xxxx_type, event_data_str = match
        
        event = {
            'event_number': int(event_num),
            'timestamp': timestamp,
            'agent': agent,
            'type': xxxx_type,
            'raw_event_data': event_data_str.strip()
        }
        
        # If it's messages type, try to parse AIMessageChunk
        if xxxx_type == 'messages':
            aimessage_data = extract_aimessagechunk_data(event_data_str)
            if aimessage_data:
                event['aimessage_chunk'] = aimessage_data
            
            metadata = extract_metadata(event_data_str)
            if metadata:
                event['metadata'] = metadata
                # Extract langgraph_id
                if 'langgraph_checkpoint_ns' in metadata:
                    event['langgraph_id'] = metadata['langgraph_checkpoint_ns']
        
        events.append(event)
    
    return events


def format_event_output(event: Dict[str, Any]) -> str:
    """Format output for a single event"""
    output = []
    output.append(f"=" * 80)
    output.append(f"Event #{event['event_number']} - {event['timestamp']}")
    output.append(f"Agent: {event['agent']}")
    output.append(f"Type: {event['type']}")
    output.append("-" * 80)
    
    # If there's AIMessageChunk data, format output in compact style
    if 'aimessage_chunk' in event:
        aimessage = event['aimessage_chunk']
        
        # langgraph_id
        if 'langgraph_id' in event:
            output.append(f"\tlanggraph_id: {event['langgraph_id']}")
        
        # content
        if 'content' in aimessage:
            content_str = json.dumps(aimessage['content'], ensure_ascii=False)
            output.append(f"\tcontent: {content_str}")
        
        # additional_kwargs
        if 'additional_kwargs' in aimessage:
            if isinstance(aimessage['additional_kwargs'], dict):
                kwargs_str = json.dumps(aimessage['additional_kwargs'], ensure_ascii=False)
            else:
                kwargs_str = str(aimessage['additional_kwargs'])
            output.append(f"\tadditional_kwargs: {kwargs_str}")
        
        # response_metadata
        if 'response_metadata' in aimessage:
            if isinstance(aimessage['response_metadata'], dict):
                metadata_str = json.dumps(aimessage['response_metadata'], ensure_ascii=False)
            else:
                metadata_str = str(aimessage['response_metadata'])
            output.append(f"\tresponse_metadata: {metadata_str}")
        
        # id
        if 'id' in aimessage:
            id_str = json.dumps(aimessage['id'], ensure_ascii=False)
            output.append(f"\tid: {id_str}")
        
        # tool_calls - with proper indentation
        if 'tool_calls' in aimessage:
            if isinstance(aimessage['tool_calls'], (list, dict)):
                tool_calls_str = json.dumps(aimessage['tool_calls'], ensure_ascii=False, indent=2)
                # Add proper indentation
                indented_tool_calls = '\n'.join(['\t\t' + line if line.strip() else line 
                                               for line in tool_calls_str.split('\n')])
                output.append(f"\ttool_calls:{indented_tool_calls}")
            else:
                output.append(f"\ttool_calls: {str(aimessage['tool_calls'])}")
        
        # tool_call_chunks - with proper indentation
        if 'tool_call_chunks' in aimessage:
            if isinstance(aimessage['tool_call_chunks'], (list, dict)):
                chunks_str = json.dumps(aimessage['tool_call_chunks'], ensure_ascii=False, indent=2)
                # Add proper indentation
                indented_chunks = '\n'.join(['\t\t' + line if line.strip() else line 
                                           for line in chunks_str.split('\n')])
                output.append(f"\ttool_call_chunks:{indented_chunks}")
            else:
                output.append(f"\ttool_call_chunks: {str(aimessage['tool_call_chunks'])}")
    
    # If only langgraph_id without aimessage_chunk
    elif 'langgraph_id' in event:
        output.append(f"\tlanggraph_id: {event['langgraph_id']}")
    
    output.append("")
    return "\n".join(output)


def main():
    """Main function"""
    input_file = "/Users/<USER>/git/ecs-deep-diagnose/backend/scripts/langgraph_event.txt"
    output_file = "/Users/<USER>/git/ecs-deep-diagnose/backend/scripts/langgraph_event_readable.txt"
    
    print("Starting to parse langgraph_event.txt file...")
    
    # Parse events
    events = parse_langgraph_events(input_file)
    
    print(f"Parsed {len(events)} events in total")
    
    # Generate formatted output
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("LangGraph Events Analysis Report\n")
        f.write("=" * 80 + "\n")
        f.write(f"Total Events: {len(events)}\n")
        f.write("=" * 80 + "\n\n")
        
        for event in events:
            formatted_output = format_event_output(event)
            f.write(formatted_output)
    
    print(f"Parsing completed! Results saved to: {output_file}")
    
    # Statistics
    message_events = [e for e in events if e['type'] == 'messages']
    update_events = [e for e in events if e['type'] == 'updates']
    
    print(f"\nStatistics:")
    print(f"- Message events: {len(message_events)}")
    print(f"- Update events: {len(update_events)}")
    print(f"- Other events: {len(events) - len(message_events) - len(update_events)}")
    
    # Show first few langgraph_ids
    langgraph_ids = [e.get('langgraph_id') for e in events if e.get('langgraph_id')]
    unique_ids = list(set(langgraph_ids))
    print(f"\nFound langgraph_ids: {len(unique_ids)} unique")
    for i, lid in enumerate(unique_ids[:10]):  # Show only first 10
        print(f"  {i+1}. {lid}")
    if len(unique_ids) > 10:
        print(f"  ... and {len(unique_ids) - 10} more")


if __name__ == "__main__":
    main()