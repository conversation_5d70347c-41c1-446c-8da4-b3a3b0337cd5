# MCP 模块使用指南

本指南旨在帮助开发者理解和使用 MCP (Model Context Protocol) 模块。通过问答的形式，您可以快速了解其核心概念、主要组件和常见用法。

---

### **1. 问：什么是 MCP 工具？**

**答：** MCP（Model Context Protocol）是 Anthropic 推出的一个开放协议，旨在让大型语言模型（LLM）能够与外部工具和数据源实现无缝通信。它采用客户端–服务器架构，通过标准化的接口，使得 LLM 应用可以安全、高效地连接到各种数据源和工具。

---

### **2. 问：本项目中的 MCP 相关模块有哪些，它们各自的作用是什么？**

**答：** MCP 模块主要包含以下四个核心组件：

1.  **`MCPToolManager`**：作为 MCP 模块的统一入口和协调器，它负责管理工具的整个生命周期。`MCPToolManager` 整合了其他组件的功能，对外提供简洁的接口，例如获取所有启用的工具 (`get_enabled_mcp_tools`) 或生成工具描述文档 (`get_enabled_mcp_tools_description`)。开发者主要与该管理器进行交互。
2.  **`MCPCacheManager`**：为了提升性能并减少对 MCP 服务器的重复请求，该组件负责工具信息的缓存。它使用 Redis 作为缓存后端，将从服务器获取的工具定义进行存储和检索。当请求工具时，系统会首先检查缓存，只有在缓存未命中或过期时，才会通过 `MCPToolClient` 发起新的请求。
3.  **`MCPToolClient`**：作为与外部 MCP 服务器通信的客户端，它负责根据配置信息（如 URL、路径、认证令牌）发起网络请求，以获取原始的工具定义。`MCPToolClient` 处理了底层的 HTTP 通信细节，确保能够安全、可靠地从服务器拉取数据。
4.  **`MCPToolConfig`**：该组件专门负责解析和管理与 MCP 相关的配置。它从应用的全局配置（例如 `config_daily.yaml`）中读取 MCP 服务器的地址、认证方式、启用的工具列表等信息，并将其结构化，供其他组件（特别是 `MCPToolClient`）使用。

---

### **3. 问：如何新增一个 MCP 工具的配置？**

**答：** 新增 MCP 工具配置，需要在对应的环境配置文件中进行设置，例如 `config_daily.yaml` 和 `config_prod.yaml`。

路径参考：
- `backend/src/deep_diagnose/common/config/files/config_daily.yaml`
- `backend/src/deep_diagnose/common/config/files/config_prod.yaml`

配置示例如下：

```yaml
  vm_coredump: # 工具组名称
    protocol: streamable_http # 协议类型
    base_url: https://ecs-mcp.alibaba-inc.com # 服务器地址
    path: /vm_coredump/mcp/ # 路径
    token: ************************************************ # 认证信息
    auth: token # 认证方式
    enabled_tools: # 启用的工具列表
      - get_vm_coredump # 工具名称
```

下图展示了在配置文件中的具体位置：

![MCP 服务器配置示例](../assets/png/10_mcp_servers_config.png)
*图6-3: MCP 服务器配置示例*

---

### **4. 问：如何获取所有已启用的 MCP 工具？**

**答：** 调用 `MCPToolManager` 实例的 `get_enabled_mcp_tools()` 方法，即可获取所有在配置中启用的 MCP 工具。

**代码示例：**
```python
tool_manager = MCPToolManager()
tools = await tool_manager.get_enabled_mcp_tools()
```

---

### **5. 问：如何生成 MCP 工具的描述文档？**

**答：** 使用 `get_enabled_mcp_tools_description()` 方法可以生成所有启用工具的详细描述文档。该功能可用于自动化生成 API 文档或在用户界面中展示工具说明。

**代码示例：**
```python
tool_manager = MCPToolManager()
tools_description = await tool_manager.get_enabled_mcp_tools_description()
```