# 配置管理模块使用指南

本指南旨在帮助开发者理解和使用配置管理模块。通过问答的形式，您可以快速了解其核心概念、主要组件和常见用法。

---

### **1. 问：什么是配置管理模块？**

**答：** 配置管理模块是一个用于**统一管理**应用配置的系统。它支持从 **YAML 文件**、**环境变量**等多种来源加载配置，并提供**安全配置管理**、**多环境支持**等核心功能，确保应用在不同部署环境下都能正确、安全地运行。

---

### **2. 问：配置管理模块的主要组件有哪些？**

**答：** 配置管理模块主要由以下核心组件构成：

1.  **`BaseConfig`**：抽象基类，定义了配置操作的基础接口，如加载 (`load`)、获取 (`get`) 和重载 (`reload`)。
2.  **`YamlConfigLoader`**：负责 **YAML** 配置文件的加载与解析。它会根据当前环境自动选择 `config_daily.yaml` 或 `config_prod.yaml`。
3.  **`EnvConfigLoader`**：负责从**环境变量**中加载配置，并支持类型转换。
4.  **`SecurityConfig`**：安全管理器，专门负责敏感信息的**加密与解密**。
5.  **`KeyCenterManager`**：密钥管理中心，提供实际的加解密能力，与 `SecurityConfig` 协同工作。

---

### **3. 问：如何添加新的配置？**

**答：** 在项目的配置文件目录中，可以向对应的 YAML 文件添加配置。系统会根据环境加载不同的文件：
- **非生产环境** (如 `daily`, `development`): 会加载 `config_daily.yaml`。
- **生产环境** (如 `pre`, `prod`): 会加载 `config_prod.yaml`。

文件路径： `backend/src/deep_diagnose/common/config/files/`

---

### **4. 问：如何访问具体的配置项？**

**答：** 首先，通过 `get_config()` 函数获取全局配置对象，然后使用点符号 (`.`) 来访问具体的配置项。

**代码示例：**
```python
from deep_diagnose.common.config import get_config

# 获取配置对象
config = get_config()

# 访问不同层级的配置
app_name = config.app.name
redis_host = config.infrastructure.redis.host
oss_endpoint = config.observability.oss.endpoint
```

---

### **5. 问：如何对敏感数据进行加密？**

**答：** 敏感数据（如 API 密钥、数据库密码等）的加密操作在 **KeyCenter** 中完成。

请访问以下地址进行操作：
[https://kc-cn.alibaba-inc.com/key/list?name=ecs-deep-diagnose_aone_key](https://kc-cn.alibaba-inc.com/index?spm=a1z3jw.map.0.0.5e8963ccZaifTg#/key/list?activeKey=all&name=ecs-deep-diagnose_aone_key)

---

### **6. 问：如何在配置文件中添加加密后的敏感数据？**

**答：** 添加敏感数据时，需要先在 KeyCenter 中加密，然后将加密后的值（例如 `&qwen_ak`）填入 YAML 配置文件，并使用 `!decrypt` 标签进行标记。

**第一步：在 KeyCenter 中定义密钥。**
![在KeyCenter中定义密钥](../assets/png/08_security_keycenter.png)
*图6-1: 在KeyCenter中定义密钥*

**第二步：在 YAML 中使用 `!decrypt` 标签引用加密数据。**
`*qwen_ak` 是加密数据的引用，`*keycenter_pub_name` 是对应的公钥名称。
![在YAML中使用!decrypt标签](../assets/png/09_security_yaml_usage.png)
*图6-2: 在YAML中使用!decrypt标签*

---

### **7. 问：如何根据不同环境加载配置？**

**答：** 系统通过环境变量 **`APP_ENV`** 来自动检测当前环境。`get_environment()` 函数会读取此变量，并返回当前环境的枚举值。

**代码示例：**
```python
from deep_diagnose.common.config.core.environment import get_environment, is_production_environment

# 获取当前环境（如 'daily', 'prod'）
current_env = get_environment()

# 判断是否为生产环境
is_prod = is_production_environment(current_env)

# get_config_file_name() 会根据环境返回 "config_daily.yaml" 或 "config_prod.yaml"
from deep_diagnose.common.config.core.environment import get_config_file_name
config_file = get_config_file_name(current_env)
```

---

### **8. 问：如何使用项目中预定义的常量？**

**答：** 可以直接从 `constants` 模块导入项目预定义的一些常量，例如内置问题或搜索引擎选项。

**代码示例：**
```python
from deep_diagnose.common.config.constants.questions import BUILT_IN_QUESTIONS_ZH_CN
from deep_diagnose.common.config.constants.tools import SELECTED_SEARCH_ENGINE
```

---

### **9. 问：如何调试配置相关的问题？**

**答：** 如果遇到配置加载或读取失败的问题，可以按以下步骤排查：

1.  **检查 YAML 文件**：确认文件路径是否正确，内容格式（缩进、语法）是否无误。
2.  **检查环境变量**：确认 `APP_ENV` 环境变量是否已正确设置。
3.  **检查加密配置**：如果问题与加密数据有关，请确保 KeyCenter 的密钥和相关权限配置正确。

---

### **10. 问：配置的完整加载流程是怎样的？**

**答：** 配置加载遵循以下流程，确保了灵活性和安全性：

1.  **环境检测**：通过 `get_environment()` 函数确定当前环境（如 `daily`、`prod`）。
2.  **文件加载**：根据环境是生产还是非生产，加载 `config_prod.yaml` 或 `config_daily.yaml`。
3.  **环境变量覆盖**：加载并解析环境变量，覆盖 YAML 文件中的同名配置项。
4.  **安全解密**：自动对 YAML 文件中标记为 `!decrypt` 的敏感数据进行解密。
5.  **配置缓存**：将最终生成的配置对象缓存到内存中，供应用全局高效使用。

![配置加载流程图](../assets/png/04_config_loading_flow.png)
*图4-1: 配置加载流程图*