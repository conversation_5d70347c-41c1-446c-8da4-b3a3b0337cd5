# 配置与安全

## 目录
- [1 配置系统设计](#1-配置系统设计)
  - [1.1 配置加载流程](#11-配置加载流程)
  - [1.2 配置文件结构](#12-配置文件结构)
- [2 安全设计](#2-安全设计)
  - [2.1 认证与授权 (Authentication & Authorization)](#21-认证与授权-authentication-authorization)
  - [2.2 数据安全 (Data Security)](#22-数据安全-data-security)
  - [2.3 运行时安全 (Runtime Security)](#23-运行时安全-runtime-security)


## 1 配置系统设计

一个健壮、灵活的配置系统是保障应用在不同环境（开发、测试、生产）中稳定运行的关键。本系统采用分层、多源的配置加载策略，确保了高度的灵活性和环境隔离。

### 1.1 配置加载流程

配置系统在应用启动时，通过 `get_config()` 函数首次被调用时，会执行一次加载和合并流程。后续的调用将直接从内存缓存中读取，以保证高性能。

![配置系统加载流程图](../../assets/png/04_config_loading_flow.png)

<div align="center"><em>图4-1: 配置系统加载流程图</em></div>

1.  **环境检测**：首先，系统通过读取 `APP_ENV` 环境变量来确定当前运行环境（如 `daily`, `prod`）。
2.  **加载 YAML 文件**：根据检测到的环境，加载对应的基础配置文件（如 `config_daily.yaml` 或 `config_prod.yaml`）。此文件定义了所有配置项的默认值。
3.  **环境变量覆盖**：系统会遍历所有环境变量，如果存在与 YAML 中配置项路径匹配的变量（如 `LLM_PROFILES_REASONING_MODEL` 对应 `llm.profiles.reasoning.model`），则环境变量的值会 **覆盖** YAML 中的默认值。这为在生产环境中通过容器编排系统注入配置提供了极大的便利。
4.  **注入动态配置**：系统会动态地注入一些运行时信息到配置对象中，例如 `is_production`、`app_name` 等。
5.  **返回与缓存**：最终生成的配置被封装在一个 `DotDict` 对象中（支持点号访问，如 `config.app.name`），并缓存在内存中，供应用全局访问。

### 1.2 配置文件结构

YAML 配置文件 (`backend/src/deep_diagnose/common/config/files/`) 具有清晰的层级结构。

```yaml
# 应用基础配置
app:
  name: ecs-deep-diagnose
  port: 8000

# 工作流相关配置
workflow:
  max_steps: 10

# 大语言模型配置
llm:
  profiles:
    reasoning:
      model: qwen-max
      # ...

# 安全相关配置
security:
  key_center_public_name: &keycenter_pub_name ecs-deep-diagnose_aone_key
  secrets_vault:
    qwen_api_key: &qwen_ak vZnIEe... # 加密后的密钥
```

## 2 安全设计

安全是系统的生命线。我们从认证授权、数据安全和运行时安全三个维度来保障系统安全。

### 2.1 认证与授权 (Authentication & Authorization)

*   **JWT 认证**：所有需要保护的 API 接口都通过 JWT (JSON Web Token) 进行认证。用户通过登录获取 Token，并在后续请求的 `Authorization` 头中携带它。
*   **SSO 集成**：系统支持与内部的单点登录（SSO）系统（如 BUC）集成，实现统一的用户认证。
*   **用户上下文**：通过 `get_current_user` 依赖注入函数，可以方便地在每个请求中获取当前登录用户的信息，为后续的权限控制和审计提供依据。

### 2.2 数据安全 (Data Security)

*   **密钥管理**：所有敏感信息（如数据库密码、LLM API Key）都 **严禁** 以明文形式存储在代码或配置文件中。它们必须通过内部的 **密钥中心（KeyCenter）** 进行加密。
*   **配置解密**：配置文件中只存储加密后的密钥字符串。系统在加载配置时，会通过特殊的 YAML 标签 `!decrypt` 自动调用密钥中心的解密服务，将加密串还原为明文密钥，并注入到配置对象中。应用代码获取到的始终是解密后的密钥，整个过程对开发者透明。

### 2.3 运行时安全 (Runtime Security)

*   **代码执行沙箱**：`Coder` 智能体所使用的 Python REPL（代码执行器）运行在一个严格隔离的沙箱环境中，限制了其文件系统访问、网络访问和系统调用权限，防止执行恶意代码。