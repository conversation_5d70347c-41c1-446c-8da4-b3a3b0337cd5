# ECS 深度诊断系统 - 后端设计文档 (V2)

## 1 引言

阅读建议：
- 建议按“01 总览与架构 → 02 核心机制 → 03 服务与工具 → 04 API 与安全 → 05 研发规范”的顺序阅读。
- 若要从使用入手，可先读“04 API 与安全 → 03 服务与工具 → 02 核心机制”。
- 若要从实现入手，建议先读“01 总览与架构”，再深入核心机制与服务模块。

本文档是 **ECS 深度诊断系统** 后端服务的核心设计与实现说明。V2 版本旨在提供一个逻辑严密、图文并茂、与当前代码实现高度一致的权威参考。

本文档为所有开发者、架构师和运维人员提供系统内部工作原理的深度洞察，确保团队对系统有统一和清晰的理解，并为未来的功能迭代和维护提供坚实的基础。

---

## 2 文档结构（分层导航）

本设计文档采用分层、多文件的结构，通过以下章节全面阐述系统设计的各个方面。请循序阅读，以建立对系统的完整认知。

- 01 总览与架构
  - [系统概述](./01_总览与架构/01_系统概述.md)
  - [架构设计](./01_总览与架构/02_架构设计.md)
  - [三层架构设计](./01_总览与架构/03_三层架构设计.md)

- 02 核心机制
  - [核心工作流](./02_核心机制/01_核心工作流.md)
  - [研究员智能体与执行机制](./02_核心机制/02_研究员智能体与执行机制.md)
  - [LangGraph 消息处理架构](./02_核心机制/03_LangGraph消息处理架构.md)
  - [ReAct 智能体与阶段化提示](./02_核心机制/04_ReAct智能体与阶段化提示.md)

- 03 服务与工具
  - [任务服务 (TaskService)](./03_服务与工具/01_任务服务.md)
  - [报告生成器 (ReportGenerator)](./03_服务与工具/02_报告生成器.md)
  - [SSE 消息解析器](./03_服务与工具/03_SSE消息解析器.md)
  - [MCP 配置与连接](./03_服务与工具/04_MCP配置与连接.md)

- 04 API 与安全
  - [API 接口规范](./04_API与安全/01_API接口规范.md)
  - [配置与安全](./04_API与安全/02_配置与安全.md)

- 05 研发规范
  - [代码结构与规范](./05_研发规范/01_代码结构与规范.md)

