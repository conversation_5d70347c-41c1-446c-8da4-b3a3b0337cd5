# Redis Pub/Sub 与缓存架构

## 1. 核心思想

为了在提供低延迟实时流的同时保证系统的最终一致性和高可用性，我们设计了一套“双轨制”的 Redis 通信与缓存架构。

- **实时轨道 (Pub/Sub)**: 利用 Redis 的发布/订阅功能，为长时间运行的异步任务提供毫秒级的事件推送，确保前端能够实时接收 Agent 的思考过程和结果。
- **回放与快照轨道 (Cache)**: 利用 Redis 的键值对（Key-Value）缓存，存储每个任务的最新事件快照。这不仅为 Pub/Sub 失败时提供了可靠的回退（Fallback）机制，也使得历史消息查询能够快速展示任务的最终或当前状态。

## 2. 架构图

*此图展示了从请求进入到事件流式返回的完整过程，包括 Pub/Sub 优先和缓存回退两种路径。*

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant ChatService as ChatService
    participant AsyncProcessor as AsynchronousRequestProcessor
    participant Agent as Agent (后台任务)
    participant EventBus as ChatEventBus
    participant Redis

    Client->>+ChatService: 发起异步请求 (request_id)
    ChatService->>+AsyncProcessor: process(context)
    AsyncProcessor->>+Redis: 订阅频道 (subscribe)
    Note right of Redis: Channel: CHAT_EVENT:channel:{request_id}
    AsyncProcessor->>Client: 返回 AsyncGenerator
    AsyncProcessor->>Agent: asyncio.create_task(_run_agent_in_background)

    activate Agent
    Agent->>+EventBus: 循环发布事件 (publish)
    EventBus->>+Redis: PUBLISH channel, EventEnvelope
    Redis-->>-EventBus: OK
    EventBus-->>-Agent: OK

    alt 定期写入快照 (节流)
        Agent->>+EventBus: write_snapshot
        EventBus->>+Redis: SET key, EventEnvelope (TTL 6h)
        Note right of Redis: Key: CHAT_EVENT:{request_id}
        Redis-->>-EventBus: OK
        EventBus-->>-Agent: OK
    end

    deactivate Agent

    AsyncProcessor->>+Redis: aiter_messages (监听频道)
    Redis-->>-AsyncProcessor: 返回事件流
    AsyncProcessor-->>Client: yield event (通过生成器)

    alt Pub/Sub 异常或超时
        AsyncProcessor->>+Redis: 回退轮询 (read_snapshot)
        Redis-->>-AsyncProcessor: 返回缓存的快照
        AsyncProcessor-->>Client: yield event
    end

    ChatService-->>-Client: 完成响应
```

## 3. 关键组件

### 3.1. `ChatEventBus`

- **定位**: 领域事件总线，封装了所有与 Redis 的底层交互。
- **职责**:
    - **命名约定**: 根据 `request_id` 生成统一的 `channel` 和 `key` 名称。
    - **发布**: 将 `BaseAgentOutputEvent` 包装在 `EventEnvelope`（含序列号 `seq`）中，并发布到 Pub/Sub 频道。
    - **快照**: 将最新的 `EventEnvelope` 写入 Redis 缓存，并设置 6 小时 TTL。
    - **订阅**: 提供一个统一的 `subscribe` 方法，返回一个可异步迭代的 `RedisPubSubStream` 对象。

### 3.2. `RequestProcessor` (抽象基类)

- **定位**: 请求处理策略接口。定义了一个 `process` 方法，使得 `ChatService` 可以根据请求类型（同步/异步）选择不同的实现策略。

### 3.3. `AsynchronousRequestProcessor`

- **定位**: 异步（长时间运行）请求的处理器。
- **核心流程**:
    1. **立即订阅**: 在任务开始前，立刻通过 `ChatEventBus` 订阅事件频道，避免丢失任何早期事件。
    2. **后台执行**: 将 Agent 的执行逻辑 (`_run_agent_in_background`) 放入 `asyncio.create_task`，使其在后台运行。
    3. **流式返回**:
        - **Pub/Sub 优先**: 优先从 Pub/Sub 流 (`_stream_via_pubsub`) 中异步读取事件并返回。内部通过 `seq` 和缓冲区保证事件的有序性。
        - **自动回退**: 如果订阅失败或流中断，则自动切换到 `_stream_from_redis`，通过低频轮询（100ms）缓存来获取更新。
    4. **任务完成**: 当检测到最终事件（如 `ErrorEvent` 或 `is_final_event` 为 `True` 的事件）时，停止流式传输。

### 3.4. `SynchronousRequestProcessor`

- **定位**: 同步（快速响应）请求的处理器。
- **核心流程**: 直接调用 Agent 的 `astream` 方法，并将其事件流同步地 `yield` 给调用方。不涉及 Redis Pub/Sub 或缓存。

### 3.5. `ChatService`

- **定位**: 聊天服务的统一入口（Facade）。
- **职责**:
    - **策略选择**: 根据 Agent 类型判断是同步还是异步，并选择相应的 `RequestProcessor`。
    - **历史消息**: 在 `get_session_messages` 中，如果消息状态为 `EXECUTING`，则通过 `ChatEventBus.read_snapshot` 获取最新的AI响应内容，以供前端展示。

## 4. 数据流与可靠性

### 4.1. 异步任务数据流

1.  **客户端** -> **`ChatService.chat`**: 发起请求。
2.  **`ChatService`** -> **`AsynchronousRequestProcessor.process`**: 委托处理。
3.  **`AsynchronousRequestProcessor`**:
    - 立即向 **Redis** 订阅频道。
    - 启动 **Agent** 后台任务。
    - 返回一个异步生成器给 **客户端**。
4.  **Agent** (后台):
    - -> **`ChatEventBus.publish`** -> **Redis Channel**: 实时发布事件。
    - -> **`ChatEventBus.write_snapshot`** -> **Redis Cache**: 节流写入快照。
5.  **`AsynchronousRequestProcessor`** (前台):
    - 从 **Redis Channel** 读取事件流，`yield` 给 **客户端**。
    - (异常时) 从 **Redis Cache** 轮询快照，`yield` 给 **客户端**。

### 4.2. 可靠性保证

- **自动降级**: Pub/Sub 路径的任何异常（连接、解析）都会触发向轮询缓存路径的平滑切换，对用户透明。
- **数据保底**: 即使 Pub/Sub 完全失败，快照缓存（TTL 6小时）确保了数据不会丢失，并且最终状态可被查询。
- **有序交付**: `EventEnvelope` 中的 `seq` 序列号确保了即使在网络延迟或乱序的情况下，消费端也能按正确顺序处理事件。
- **幂等性**: 消费端通过 `last_event_data` 检查，避免重复处理相同的事件数据。

## 5. 故障排查

- **检查频道活跃度**: `redis-cli PUBSUB CHANNELS CHAT_EVENT:channel:*`
- **检查快照键**: `redis-cli KEYS "CHAT_EVENT:req_*"`
- **模拟订阅**: `redis-cli SUBSCRIBE CHAT_EVENT:channel:<request_id>`
