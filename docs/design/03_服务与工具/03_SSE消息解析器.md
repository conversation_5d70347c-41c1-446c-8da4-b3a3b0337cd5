# SSE 消息解析器 (SSEMessageParser)

## 目录
- [1 概述](#1-概述)
- [2 核心职责](#2-核心职责)
- [3 架构设计](#3-架构设计)
  - [3.1 核心组件：](#31-核心组件：)
- [4 事件处理流程](#4-事件处理流程)
- [5 关键代码路径](#5-关键代码路径)


## 1 概述

`SSEMessageParser` 是一个关键的实时通信组件，它负责解析来自 LangGraph 工作流的 Server-Sent Events (SSE) 事件流。这些事件包含了智能体在执行诊断任务过程中的实时输出，例如思考过程、工具调用、工具执行结果等。解析器将这些原始事件转换为结构化的数据，供前端实时展示。

## 2 核心职责

*   **实时事件解析**：接收并解析符合 SSE 格式的事件字符串。
*   **事件类型识别**：根据事件的 `event` 字段或 `data` 内容，智能识别事件类型（如 `message_chunk`, `tool_calls`, `tool_call_chunks`, `tool_call_result`）。
*   **数据结构化**：将原始的 JSON 字符串数据解析为 Python 对象，并聚合到统一的 `ParsedData` 模型中。
*   **工具执行跟踪**：跟踪每个工具调用的完整生命周期，包括参数的累积和结果的记录。
*   **多智能体数据聚合**：聚合来自不同智能体（`planner`, `researcher`, `coder`, `reporter` 等）的输出。
*   **状态管理**：维护内部状态，如 `thread_to_tool` 映射，以正确关联分块传输的数据。

## 3 架构设计

`SSEMessageParser` 采用事件驱动的处理模式，内部包含多个处理器来应对不同类型的 SSE 事件。

```mermaid
graph TD
    A[SSE 事件流] --> B(SSEMessageParser)
    B --> C[事件解析器]
    C --> D{事件类型判断}
    
    D -->|message_chunk| E[消息块处理器]
    D -->|tool_calls| F[工具调用处理器]
    D -->|tool_call_chunks| G[工具参数块处理器]
    D -->|tool_call_result| H[工具结果处理器]
    
    E --> I[更新 ParsedData]
    F --> I
    G --> I
    H --> I
    
    I --> J[前端实时更新]
    
    subgraph 数据模型
        K[ToolExecution]
        L[ParsedData]
    end
    
    I --> K
    I --> L
```

<div align="center"><em>图6-2: SSE 消息解析器架构图</em></div>

### 3.1 核心组件：

*   **`SSEMessageParser`**：主协调器，负责接收原始 SSE 事件，并将其分发给相应的内部处理器。
*   **事件解析器**：负责将原始的 SSE 字符串解析为 `event` 类型和 `data` 内容。具备智能推断事件类型的能力。
*   **消息块处理器**：处理智能体生成的文本内容块，如 `planner` 的思考过程、`reporter` 的报告内容。
*   **工具调用处理器**：处理智能体发起的工具调用事件，创建 `ToolExecution` 记录。
*   **工具参数块处理器**：处理工具参数的分块传输，将参数片段累积到对应的 `ToolExecution` 记录中。
*   **工具结果处理器**：处理工具执行完毕后的结果，更新 `ToolExecution` 记录。
*   **`ParsedData`**：一个聚合数据模型，包含了所有解析后的结构化信息，如诊断计划、研究员数据、报告内容和工具执行记录。
*   **`ToolExecution`**：一个数据模型，用于跟踪单个工具调用的详细信息，包括工具 ID、名称、参数和执行结果。

## 4 事件处理流程

1.  **接收 SSE 事件**：`process_sse_event` 方法接收一个完整的 SSE 事件字符串。
2.  **解析事件格式**：内部方法 `_parse_sse_event` 提取 `event` 类型和 `data` 内容。如果 `event` 类型缺失，则根据 `data` 内容智能推断。
3.  **分发处理**：根据事件类型，将 `data` 分发给对应的 `_handle_message_chunk`、`_handle_tool_calls`、`_handle_tool_chunks` 或 `_handle_tool_result` 方法。
4.  **数据聚合与更新**：各个处理器将解析后的数据更新到 `ParsedData` 对象中。对于关键更新（如工具调用完成、计划解析完成），会返回 `True`，通知上层进行实时推送。

## 5 关键代码路径

*   **`backend/src/deep_diagnose/services/sse/sse_message_parser.py`**：`SSEMessageParser` 的主要实现文件。
*   **`backend/src/deep_diagnose/api/routes/chat.py`**：API 路由层中处理 SSE 流的入口。
*   **`backend/src/deep_diagnose/core/workflow/types.py`**：定义了 `State` 中包含的 `ParsedData` 等数据模型。