# MessageAsyncQueue 与事件处理流水线设计

## 1. 概述

为了解决 `ReasoningAgent` 中复杂事件流的同步处理所带来的阻塞和耦合问题，我们引入了一套全新的、基于异步消息队列的事件处理流水线。该设计遵循“生产者-消费者”模式，将事件的接收与处理彻底解耦，提高了系统的健壮性、可扩展性和可维护性。

核心思想：
- **异步解耦**: `ReasoningAgent` 作为生产者，将从 LangGraph 收到的原始事件快速放入一个 `asyncio.Queue` 中，不做任何耗时处理。
- **后台消费**: 一个独立的后台协程作为消费者，从队列中串行消费事件，保证处理的顺序性。
- **分层处理**: 事件处理被划分为三个明确的阶段：**收集 (Collect)** -> **存储 (Store)** -> **处理 (Process)**，每一阶段职责单一。
- **中央仓储**: `MessageRepository` 作为内存中的中央数据仓库，存储所有 Agent 在一次运行中的完整状态，供处理阶段查询。

## 2. 架构图

*此图展示了从 LangGraph 原始事件到最终结构化业务事件的完整处理流水线。*

```mermaid
graph TD
    subgraph ReasoningAgent.astream (事件生产者)
        A[LangGraph Raw Event] --> B{ReasoningAgentEvent};
        B --> |message_chunk, metadata| C[asyncio.Queue];
    end

    subgraph "后台协程 (_processor_loop)"
        C --> D[取出事件];
    end

    subgraph "事件处理流水线"
        D --> E[1. 收集 (MessageCollector)];
        E --> |更新 AgentMessage| F[2. 存储 (MessageRepository)];
        F --> |查询 AgentMessage| G[3. 处理 (BusinessMessageProcessor)];
        G --> H[Agent子处理器<br/>(Planner/Researcher/Reporter)];
        H --> |结构化业务数据| I[更新 ReasoningAgentEvent 状态];
    end

    subgraph "SSE 流式输出"
        I --> |检测到 _version 变更| J[yield ReasoningAgentEvent];
        J --> K[客户端];
    end

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#ccf,stroke:#333,stroke-width:2px
```

## 3. 关键组件

### 3.1. `asyncio.Queue` (消息队列)

- **定位**: 核心的解耦组件。
- **职责**: 作为 `ReasoningAgent` 主事件流和后台处理逻辑之间的缓冲区。生产者 (`ReasoningAgent`) 只需快速 `put_nowait`，消费者 (`_processor_loop`) 则 `await queue.get()`，实现了完美的异步协作。

### 3.2. `MessageRepository` (中央仓储)

- **定位**: 内存中的单一事实来源 (Single Source of Truth)。
- **职责**:
    - 存储所有 `AgentMessage` 对象。
    - 提供高效的索引，支持按 `run_id`、`agent_name` 和 `tool_call_id` 快速查找，避免了在处理时反复遍历列表。
- **核心数据模型**:
    - `AgentMessage`: 代表一个 Agent 单次执行的完整记录，累积了内容、工具调用和原始消息片段。
    - `ToolExecution`: 封装单次工具调用的状态和结果。

### 3.3. `MessageCollector` (收集器)

- **定位**: 流水线的第一阶段，原始事件的解析器。
- **职责**:
    - 接收原始的 `AIMessageChunk` 或 `ToolMessage`。
    - 解析消息，识别其归属的 `AgentMessage` (通过 `run_id`)。
    - 将消息内容、工具调用等信息更新到 `MessageRepository` 中对应的 `AgentMessage` 对象上。

### 3.4. `BusinessMessageProcessor` (业务处理器)

- **定位**: 流水线的第三阶段，业务逻辑的编排器。
- **职责**:
    - 接收到变更通知后，根据 `agent_name` 选择一个具体的子处理器。
    - 调用子处理器，并将 `MessageRepository` 作为上下文传入。
    - 聚合子处理器的结果，形成最终的结构化业务事件。

### 3.5. Agent 子处理器 (如 `PlannerMessageProcessor`)

- **定位**: 具体的业务逻辑实现。
- **职责**:
    - 从 `MessageRepository` 中查询其关心的所有 `AgentMessage`。
    - 将这些消息转换成对用户有意义的结构化数据。例如，`PlannerMessageProcessor` 会提取出 `thought` 和 `plan_steps`。

## 4. 数据处理流程

1.  **入队**: `ReasoningAgent.astream` 接收到 LangGraph 事件后，调用 `ReasoningAgentEvent.handle_message`，将 `(message_chunk, message_metadata)` 放入 `asyncio.Queue`。
2.  **消费**: 后台的 `_processor_loop` 从队列中取出事件。
3.  **收集**: `MessageCollector.collect_message` 被调用。它会找到或创建对应的 `AgentMessage`，并用新消息块的内容（如文本增量、工具调用请求）更新它。
4.  **处理**: `BusinessMessageProcessor.process_business_messages` 被调用。它会根据触发变更的 `agent_name`，选择例如 `ResearcherMessageProcessor`。
5.  **转换**: `ResearcherMessageProcessor` 从 `MessageRepository` 获取所有 `researcher` 的消息，并将它们转换成一个 `executions` 列表。
6.  **应用**: `ReasoningAgentEvent._apply_event_data` 被调用，将 `executions` 列表更新到自身的 `self.executions` 字段上，并递增 `_version`。
7.  **通知**: `_version` 的递增会触发 `_version_changed` 事件。
8.  **输出**: `ReasoningAgent.astream` 中等待该事件的部分被唤醒，将 `ReasoningAgentEvent` 的当前完整状态序列化后 `yield` 给客户端。

这个流程确保了即使在高频、并发的事件流下，数据处理依然有序、健壮，并且最终呈现给用户的状态是完整和一致的。
