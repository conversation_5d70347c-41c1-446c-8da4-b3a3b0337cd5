# 三层架构设计

## 目录
- [1 📋 概述](#1-📋-概述)
- [2 🏗️ 整体架构](#2-🏗️-整体架构)
  - [2.1 核心交互流程](#21-核心交互流程)
- [3 🔌 API 层设计](#3-🔌-api-层设计)
  - [3.1 核心文件](#31-核心文件)
  - [3.2 主要职责](#32-主要职责)
  - [3.3 核心接口](#33-核心接口)
    - [3.3.1 流式聊天接口](#331-流式聊天接口)
    - [3.3.2 会话管理接口](#332-会话管理接口)
  - [3.4 设计要点](#34-设计要点)
- [4 ⚙️ Service 层设计](#4-⚙️-service-层设计)
  - [4.1 核心文件](#41-核心文件)
  - [4.2 主要职责](#42-主要职责)
  - [4.3 核心组件](#43-核心组件)
    - [4.3.1 ChatServiceV1 主服务类](#431-chatservicev1-主服务类)
    - [4.3.2 请求处理器 (Request Processors)](#432-请求处理器-request-processors)
    - [4.3.3 管理器组件](#433-管理器组件)
  - [4.4 设计要点](#44-设计要点)
- [5 🤖 Agent 层设计](#5-🤖-agent-层设计)
  - [5.1 核心文件](#51-核心文件)
  - [5.2 主要职责](#52-主要职责)
  - [5.3 核心组件](#53-核心组件)
    - [5.3.1 BaseGraph 抽象基类](#531-basegraph-抽象基类)
    - [5.3.2 ReasoningAgent 实现类](#532-reasoningagent-实现类)
  - [5.4 设计要点](#54-设计要点)
- [6 🎯 核心设计原则](#6-🎯-核心设计原则)
  - [6.1 单一职责原则](#61-单一职责原则)
  - [6.2 开放封闭原则](#62-开放封闭原则)
  - [6.3 依赖倒置原则](#63-依赖倒置原则)
  - [6.4 接口隔离原则](#64-接口隔离原则)
- [7 📊 性能优化策略](#7-📊-性能优化策略)
  - [7.1 缓存策略](#71-缓存策略)
  - [7.2 异步处理](#72-异步处理)
  - [7.3 流式处理](#73-流式处理)
- [8 🔧 扩展性设计](#8-🔧-扩展性设计)
  - [8.1 新 Agent 类型扩展](#81-新-agent-类型扩展)
  - [8.2 新处理策略扩展](#82-新处理策略扩展)
  - [8.3 新管理器扩展](#83-新管理器扩展)


## 1 📋 概述

本文档详细描述了 ECS Deep Diagnose 系统的 API/Service/Agent 三层架构设计，包括各层的职责、核心逻辑和交互关系。

## 2 🏗️ 整体架构

系统采用经典的三层架构模式：
- **API 层**：负责HTTP请求处理、参数验证、响应格式化
- **Service 层**：负责业务逻辑处理、会话管理、缓存策略
- **Agent 层**：负责智能推理、工具调用、事件流生成

![API Service Agent 架构图](../../assets/png/12_api_service_agent_architecture.png)

<div align="center"><em>图12-1: API Service Agent 架构图</em></div>

### 2.1 核心交互流程

下图详细展示了同步处理和异步处理两种策略的完整交互流程：

![核心交互流程图](../../assets/png/13_core_interaction_flow.png)

<div align="center"><em>图13-1: 核心交互流程图</em></div>

---

## 3 🔌 API 层设计

### 3.1 核心文件
- `backend/src/deep_diagnose/api/route/v1/chat.py`

### 3.2 主要职责
1. **HTTP 请求处理**：接收并验证客户端请求
2. **认证授权**：JWT Token 验证和用户身份确认
3. **响应格式化**：将 Service 层返回的事件流包装为标准 API 响应
4. **错误处理**：统一的异常处理和错误响应

### 3.3 核心接口

#### 3.3.1 流式聊天接口
```python
@router.post("/chat")
async def chat_v1(request: ChatRequestV1, user: UserModel, chat_service: ChatServiceV1)
```

**核心逻辑**：
- 参数验证和用户身份确认
- 调用 Service 层的 `chat()` 方法
- 将 `BaseAgentOutputEvent` 包装为标准 API 响应格式
- 通过 SSE 流式返回数据

**响应格式**：
```json
{
    "request_id": "请求追踪ID",
    "session_id": "会话ID",
    "agent": "智能体类型",
    "data": "智能体输出数据",
    "status": "processing/completed/error"
}
```

#### 3.3.2 会话管理接口
```python
@router.get("/chat/sessions")
async def get_user_sessions(user_id: str, chat_service: ChatServiceV1)

@router.get("/chat/sessions/{session_id}/messages")
async def get_session_messages(session_id: str, chat_service: ChatServiceV1)
```

### 3.4 设计要点
- **职责单一**：仅负责 HTTP 协议相关的处理
- **无状态设计**：不保存业务状态，依赖 Service 层
- **标准化响应**：统一的 API 响应格式和错误处理

---

## 4 ⚙️ Service 层设计

### 4.1 核心文件
- `backend/src/deep_diagnose/services/chat/chat_service_v1.py`
- `backend/src/deep_diagnose/services/chat/request_processors.py`
- `backend/src/deep_diagnose/services/chat/managers/`

### 4.2 主要职责
1. **业务逻辑协调**：协调各个管理器完成业务流程
2. **请求处理策略**：根据 Agent 类型选择同步/异步处理策略
3. **会话管理**：会话创建、消息管理、历史查询
4. **缓存策略**：Redis 缓存的读写和策略控制

### 4.3 核心组件

#### 4.3.1 ChatServiceV1 主服务类
```python
class ChatServiceV1:
    async def chat(self, question: str, user_id: str, agent: str, ...) -> AsyncGenerator[BaseAgentOutputEvent, None]
    async def get_user_sessions(self, user_id: str, limit: int) -> List[Dict[str, Any]]
    async def get_session_messages(self, session_id: str) -> List[Dict[str, Any]]
```

**核心逻辑**：
- 创建 `ChatContext` 聊天上下文
- 根据 Agent 类型选择处理器（同步/异步）
- 协调各个管理器完成业务流程

#### 4.3.2 请求处理器 (Request Processors)

##### 4.3.2.1 SynchronousRequestProcessor
- **适用场景**：快速响应的 Agent
- **处理流程**：
  1. 设置会话信息
  2. 创建 Agent 实例
  3. 直接流式调用 Agent
  4. 策略性缓存事件数据
  5. 持久化最终结果

##### 4.3.2.2 AsynchronousRequestProcessor
- **适用场景**：长时间运行的 Agent（如 ReasoningAgent）
- **处理流程**：
  1. 设置会话信息
  2. 启动后台任务
  3. 通过 Redis 流式返回进度
  4. 任务完成后持久化结果

#### 4.3.3 管理器组件

##### 4.3.3.1 SessionManager
```python
class SessionManager:
    @staticmethod
    async def setup_session(context) -> SessionInfo
    @staticmethod
    async def persist_ai_response(session_id: str, message_id: int, result_content: str)
```

**职责**：
- 会话创建和加载
- 用户消息记录
- AI 消息占位符和结果持久化

##### 4.3.3.2 AgentManager
```python
class AgentManager:
    @staticmethod
    def create_agent(agent_type: str, message_id: int)
 ```

**职责**：
- Agent 实例创建
- Langfuse 监控配置
- Agent 调用参数准备

##### 4.3.3.3 CacheStrategy
```python
class CacheStrategy:
    async def cache_with_strategy(self, key: str, data: str, ttl_seconds: int)
    async def force_cache(self)
```

**职责**：
- 智能缓存策略（避免频繁写入）
- 最小缓存间隔控制
- 延迟缓存和强制缓存

### 4.4 设计要点
- **策略模式**：根据 Agent 类型选择不同的处理策略
- **管理器模式**：将不同职责拆分到专门的管理器
- **异步处理**：支持长时间运行任务的异步处理
- **缓存优化**：智能缓存策略减少 Redis 压力

---

## 5 🤖 Agent 层设计

### 5.1 核心文件
- `backend/src/deep_diagnose/core/agent/base_graph.py`
- `backend/src/deep_diagnose/core/reasoning/reasoning_agent.py`

### 5.2 主要职责
1. **智能推理**：基于 LangGraph 的复杂推理流程
2. **工具调用**：集成各种诊断工具和 MCP 服务
3. **事件流生成**：生成结构化的事件流数据
4. **状态管理**：维护推理过程中的状态信息

### 5.3 核心组件

#### 5.3.1 BaseGraph 抽象基类
```python
class BaseGraph(abc.ABC):
    def __init__(self, config: Optional[Dict[str, Any]] = None)
    
    @abc.abstractmethod
    async def astream(self, question: str, **kwargs) -> AsyncIterator[BaseAgentOutputEvent]
```

**设计要点**：
- 定义所有 Graph Agent 的统一接口
- 内部状态管理（plan_iterations, final_report, current_plan, observations）
- 抽象方法确保子类实现核心功能

#### 5.3.2 ReasoningAgent 实现类
```python
class ReasoningAgent(BaseGraph):
    async def astream(self, question: str, messages: Optional[List[Dict[str, Any]]], **kwargs) -> AsyncIterator[str]
```

**核心逻辑**：
1. **初始化配置**：构建 LangGraph 工作流和配置
2. **消息处理**：将用户问题转换为 LangChain 消息格式
3. **工作流执行**：调用 LangGraph 执行复杂推理流程
4. **事件流处理**：
   - 过滤静默标签事件
   - 处理中断事件
   - 转换消息事件为 SSE 格式
   - 区分工具调用和普通消息

**事件类型**：
- `message_chunk`：AI 消息令牌
- `tool_calls`：工具调用请求
- `tool_call_chunks`：工具调用块
- `tool_call_result`：工具调用结果
- `interrupt`：中断事件
- `error`：错误事件

### 5.4 设计要点
- **基于 LangGraph**：利用 LangGraph 的强大推理能力
- **事件驱动**：通过事件流实现实时交互
- **工具集成**：支持多种诊断工具和 MCP 服务
- **状态持久化**：维护推理过程中的关键状态

---

## 6 🎯 核心设计原则

### 6.1 单一职责原则
- **API 层**：仅负责 HTTP 协议处理
- **Service 层**：专注业务逻辑协调
- **Agent 层**：专注智能推理实现

### 6.2 开放封闭原则
- 通过抽象基类支持新 Agent 类型扩展
- 通过策略模式支持新处理器扩展
- 通过管理器模式支持新功能模块扩展

### 6.3 依赖倒置原则
- API 层依赖 Service 层抽象
- Service 层依赖 Agent 层抽象
- 通过依赖注入实现松耦合

### 6.4 接口隔离原则
- 不同层次定义专门的接口
- 避免接口污染和不必要的依赖

---

## 7 📊 性能优化策略

### 7.1 缓存策略
- **智能缓存**：避免频繁 Redis 写入
- **分层缓存**：Redis + 内存缓存
- **TTL 管理**：合理的缓存过期时间

### 7.2 异步处理
- **长任务异步化**：避免 HTTP 连接超时
- **事件驱动**：通过事件流实现实时交互
- **资源池化**：复用数据库连接和 Redis 连接

### 7.3 流式处理
- **SSE 技术**：实时数据传输
- **增量更新**：只传输变化的数据
- **背压控制**：避免内存溢出

---

## 8 🔧 扩展性设计

### 8.1 新 Agent 类型扩展
1. 继承 `BaseGraph` 抽象基类
2. 实现 `astream()` 方法
3. 在 `AgentFactory` 中注册新类型

### 8.2 新处理策略扩展
1. 继承 `RequestProcessor` 抽象基类
2. 实现 `process()` 方法
3. 在 `ChatServiceV1` 中配置策略选择逻辑

### 8.3 新管理器扩展
1. 实现专门的管理器类
2. 在 Service 层中集成新管理器
3. 保持单一职责和接口清晰