# 架构设计

## 目录
- [1 总体架构](#1-总体架构)
  - [1.1 展现与接入层 (API Layer)](#11-展现与接入层-api-layer)
  - [1.2 核心业务层 (Core Business Layer)](#12-核心业务层-core-business-layer)
  - [1.3 工具与服务层 (Tools & Services Layer)](#13-工具与服务层-tools-services-layer)
  - [1.4 基础设施层 (Infrastructure Layer)](#14-基础设施层-infrastructure-layer)
- [2 设计原则](#2-设计原则)


## 1 总体架构

系统采用经典的分层架构，确保各层职责单一、边界清晰，从而实现高度的模块化和可扩展性。整体架构分为四层：展现与接入层、核心业务层、工具与服务层、基础设施层。

![系统总体架构图](../../assets/png/01_system_architecture.png)

<div align="center"><em>图1-1: 系统总体架构图</em></div>

### 1.1 展现与接入层 (API Layer)

该层是系统与外部交互的唯一入口，负责处理所有传入的 HTTP 请求。它基于 **FastAPI** 构建，具备高性能和现代化的特性。

*   **FastAPI 应用**：作为 Web 框架，负责应用的生命周期管理。
*   **认证与中间件**：处理如 CORS、JWT 认证、会话管理、请求日志等横切关注点。
*   **API 路由**：定义所有对外暴露的 RESTful API 接口，如 `/api/v1/tasks` 和 `/api/chat/stream`。

### 1.2 核心业务层 (Core Business Layer)

这是系统的大脑，包含了所有的核心业务逻辑和流程编排。

*   **多智能体工作流 (LangGraph)**：基于 LangGraph 构建的状态图（StateGraph），它定义了智能体之间的协作流程和状态转移规则，是整个诊断过程的“总指挥”。
*   **核心服务**：封装了关键的业务能力，例如：
    *   **任务服务 (TaskService)**：管理异步诊断任务的完整生命周期。
    *   **报告生成器 (ReportGenerator)**：将诊断结果格式化为人类易读的 HTML 报告。
    *   **SSE 解析器 (SSEMessageParser)**：实时解析来自工作流的事件流，供前端展示。

### 1.3 工具与服务层 (Tools & Services Layer)

该层为核心业务层提供执行具体操作的“武器库”。智能体通过调用这些工具来与外部世界交互和收集信息。

*   **MCP 工具集**：与内部运维平台（Model Context Protocol）交互，执行获取虚拟机信息、查询监控数据等底层操作。
*   **代码执行器 (REPL)**：提供一个安全的沙箱环境，用于执行 Python 代码，进行数据处理和深度分析。

### 1.4 基础设施层 (Infrastructure Layer)

为整个系统提供稳定运行所需的基础服务支撑。

*   **Redis**：用作高性能的缓存和消息队列，主要用于存储任务状态和会话信息。
*   **LLM 服务**：与多种大语言模型（如通义千问系列）的 API 进行交互，为智能体提供动力。
*   **对象存储 (OSS)**：用于持久化存储生成的诊断报告等静态文件。

## 2 设计原则

*   **高内聚，低耦合**：各层、各模块之间通过明确的接口进行通信，减少相互依赖。
*   **面向接口编程**：核心功能（如 LLM、工具）都通过抽象接口进行定义，便于未来替换或扩展实现。
*   **无状态服务**：核心业务逻辑设计为无状态，便于水平扩展和负载均衡。
*   **可观测性优先**：在系统各处都集成了详细的日志和分布式追踪（Langfuse），确保系统行为透明、可监控。