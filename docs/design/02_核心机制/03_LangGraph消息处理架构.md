# LangGraph 消息处理架构

## 目录
- [1 概述](#1-概述)
  - [1.1 核心架构](#11-核心架构)
  - [1.2 设计目标](#12-设计目标)
- [2 核心组件详解](#2-核心组件详解)
  - [2.1 2.1. 事件协调层 (`ReasoningAgentEventV2`)](#21-21-事件协调层-reasoningagenteventv2)
  - [2.2 2.2. 数据收集层 (`MessageCollector`)](#22-22-数据收集层-messagecollector)
  - [2.3 2.3. 数据存储层 (`MessageRepository`)](#23-23-数据存储层-messagerepository)
  - [2.4 2.4. 业务处理层 (`BusinessMessageProcessor` & Sub-processors)](#24-24-业务处理层-businessmessageprocessor-sub-processors)
- [3 数据模型](#3-数据模型)
  - [3.1 3.1. `AgentMessage`](#31-31-agentmessage)
  - [3.2 3.2. `ToolExecution`](#32-32-toolexecution)
  - [3.3 3.3. `MessageChunk`](#33-33-messagechunk)
- [4 核心处理流程](#4-核心处理流程)
  - [4.1 详细处理步骤](#41-详细处理步骤)
- [5 总结与后续方向](#5-总结与后续方向)
  - [5.1 总结](#51-总结)
  - [5.2 后续发展](#52-后续发展)


## 1 概述

本文档旨在详细阐述 ECS 深度诊断系统中，基于 `reasoning_events_v2.py` 的 LangGraph 消息处理架构。该架构V2版本经过重构，采用了一个清晰、简洁的三层模型，以实现高效、可维护的消息处理流程。

### 1.1 核心架构

![事件系统架构与数据模型](../../assets/png/14_event_system_architecture_and_data_model.png)

<div align="center"><em>图1-1: ECS深度诊断系统事件处理架构 - 展示了从外部输入到业务处理的完整分层架构</em></div>

系统遵循一个单向数据流模式：

**MessageCollector → MessageRepository → MessageProcessor**

1.  **MessageCollector (消息收集器)**: 负责接收和解析来自 LangGraph 的原始消息。
2.  **MessageRepository (消息仓库)**: 一个**内存中**的临时存储，负责在单次请求的生命周期内聚合和管理消息。
3.  **MessageProcessor (消息处理器)**: 负责从仓库中提取消息，并根据其业务逻辑（如 Planner, Researcher）进行处理，最终生成结构化的业务数据。

### 1.2 设计目标

*   **清晰性与可维护性**: 通过明确的职责分离，使代码易于理解和维护。
*   **可扩展性**: 方便地添加对新 Agent 类型的支持，只需实现新的处理器即可。
*   **准确性**: 文档内容严格对应代码实现，移除所有不实的设计。

---

## 2 核心组件详解

### 2.1 2.1. 事件协调层 (`ReasoningAgentEventV2`)

这是整个消息处理流程的入口和总协调者。

*   **职责**:
    *   管理 `MessageCollector`, `MessageRepository`, 和 `BusinessMessageProcessor` 的实例。
    *   提供 `handle_message` 作为外部接口，驱动整个处理流程。
    *   维护最终的业务数据字段（`thought`, `plan_steps`, `executions`, `result`, `urls`）。
    *   将从处理器返回的业务数据，更新到自身的字段中。

### 2.2 2.2. 数据收集层 (`MessageCollector`)

负责将原始、零散的 LangGraph 消息，转换为结构化的 `AgentMessage`。

*   **职责**:
    *   识别 `AIMessageChunk` 和 `ToolMessage` 两种主要消息类型。
    *   从消息元数据中提取 `agent_name`, `run_id`, `langgraph_id` 等关键信息。
    *   解析消息内容，包括文本内容 (`content`) 和工具调用 (`tool_calls`)。
    *   将解析后的数据存入 `MessageRepository`。

### 2.3 2.3. 数据存储层 (`MessageRepository`)

一个简单的**内存**存储，生命周期与单次诊断请求绑定。

*   **职责**:
    *   核心数据结构是一个 Python `list`，用于存储 `AgentMessage` 对象。
    *   提供 `get_or_create_agent_message` 方法，根据 `run_id` 聚合属于同一 Agent 运行过程的消息。
    *   提供 `get_messages_by_agent` 等查询方法，供业务处理器使用。
*   **重要特性**:
    *   **无持久化**: 数据仅在内存中，请求结束后即销毁。
    *   **无缓存策略**: 不涉及 LRU、TTL 等复杂缓存机制。
    *   **无索引**: 查询是简单的线性遍历。

### 2.4 2.4. 业务处理层 (`BusinessMessageProcessor` & Sub-processors)

这是业务逻辑的核心，采用策略模式实现。

*   **`BusinessMessageProcessor`**:
    *   作为一个分发器（Dispatcher），它持有一个从 Agent 名称到具体处理器的映射（字典）。
    *   遍历所有注册的处理器，从 `MessageRepository` 中获取对应的消息并交由其处理。

*   **`BaseMessageProcessor` (抽象基类)**:
    *   定义了所有具体处理器必须实现的接口 (`process_messages`, `get_agent_name`)。

*   **具体处理器实现**:
    *   **`PlannerMessageProcessor`**: 解析 Planner Agent 的输出，提取 `thought` (思考过程) 和 `plan_steps` (计划步骤)。
    *   **`ResearcherMessageProcessor`**: 解析 Researcher Agent 的输出，提取 `executions` (工具调用记录)。
    *   **`ReporterMessageProcessor`**: 解析 Reporter Agent 的输出，提取最终的 `result` (诊断报告)。

---

## 3 数据模型

系统的核心数据模型由以下三个 `dataclass` 构成：

### 3.1 3.1. `AgentMessage`

代表一个 Agent 在一次运行（run）中产生的所有信息的聚合。

```python
@dataclass
class AgentMessage:
    run_id: str
    agent: str
    langgraph_id: str
    tags: List[str]

    # 累积的内容
    content: str = ""
    reasoning_content: str = ""

    # 工具执行
    tool_executions: List[ToolExecution] = field(default_factory=list)

    # 原始数据片段
    message_chunks: List[MessageChunk] = field(default_factory=list)

    # 状态信息
    is_finished: bool = False
    finish_reason: Optional[str] = None
```

### 3.2 3.2. `ToolExecution`

代表一次完整的工具调用及其结果。

```python
@dataclass
class ToolExecution:
    call_id: str
    call_name: str
    call_args: Dict[str, Any]
    call_result: Optional[Dict[str, Any]] = None
    status: ToolExecutionStatus = ToolExecutionStatus.PENDING
    error_message: Optional[str] = None
```

### 3.3 3.3. `MessageChunk`

代表从 LangGraph 收到的最原始的数据片段。

```python
@dataclass
class MessageChunk:
    content: str = ""
    reasoning_content: str = ""
    tool_call_chunks: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
```

---

## 4 核心处理流程

![事件系统完整处理流程](../../assets/png/15_event_system_complete_flow.png)

<div align="center"><em>图1-2: 事件系统端到端处理流程 - 从消息接收到业务数据更新的完整处理链路</em></div>

### 4.1 详细处理步骤

1.  `ReasoningAgentEventV2.handle_message` 接收到来自 LangGraph 的一个消息（`message_chunk`）。
2.  该方法调用 `MessageCollector.collect_message`。
3.  `MessageCollector` 解析消息，并在 `MessageRepository` 中查找或创建一个对应的 `AgentMessage` 对象，然后用新消息的数据更新它。
4.  `handle_message` 接着调用 `BusinessMessageProcessor.process_business_messages`。
5.  `BusinessMessageProcessor` 遍历其注册的所有处理器（Planner, Researcher 等）。
6.  对于每个处理器，它从 `MessageRepository` 中获取该 Agent 类型的所有消息 (`get_messages_by_agent`)。
7.  获取到的消息被传递给具体处理器的 `process_messages` 方法。
8.  处理器返回一个包含业务数据的字典，例如 `{"thought": "...", "plan_steps": [...]}`。
9.  `ReasoningAgentEventV2` 将这个字典中的数据合并到自身的业务字段中（`self.thought`, `self.plan_steps`）。
10. 整个流程同步执行，直到所有消息处理完毕。

---

## 5 总结与后续方向

### 5.1 总结

当前 V2 架构是一个**轻量级、同步、内存中**的消息处理系统。它放弃了 V1 设计中过于复杂的配置和功能（如持久化、缓存、高级错误处理），专注于核心需求的实现，显著提升了代码的**可读性、可维护性和可测试性**。其核心优势在于通过策略模式实现了良好的**扩展性**，可以方便地增加新的 Agent 处理器。

### 5.2 后续发展

如果未来有更复杂的需求，可以考虑在当前清晰架构的基础上进行扩展：

1.  **异步处理**: 将 `BusinessMessageProcessor` 的处理过程改造为异步，以提高性能。
2.  **持久化**: 为 `MessageRepository` 引入一个真正的持久化后端（如 Redis），以支持更长的任务生命周期和跨实例状态共享。
3.  **错误处理**: 引入更健壮的错误处理机制，如重试、死信队列等。