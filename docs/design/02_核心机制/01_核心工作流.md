# 核心工作流

## 目录
- [1 工作流概述](#1-工作流概述)
- [2 工作流图示](#2-工作流图示)
- [3 智能体角色详解](#3-智能体角色详解)
- [4 状态管理](#4-状态管理)


## 1 工作流概述

系统的核心诊断流程是通过 **LangGraph** 构建的一个有向无环图（DAG），更具体地说，是一个状态图（StateGraph）。这个图定义了从接收用户请求到生成最终报告的完整生命周期。工作流中的每个节点（Node）代表一个智能体（Agent）或一个逻辑判断，边（Edge）则代表了状态的流转方向。

这种设计的核心优势在于其 **灵活性** 和 **可观测性**。我们可以轻易地增、删、改工作流中的节点和流转逻辑，以适应不断变化的诊断需求。同时，由于每个节点和边的执行都会被记录，我们可以清晰地追踪每一次诊断任务的完整路径。

## 2 工作流图示

下图详细展示了智能体之间的协作流程和决策路径。

![核心工作流图](../../assets/png/02_core_workflow.png)

<div align="center"><em>图2-1: 核心工作流图</em></div>

## 3 智能体角色详解

工作流由一个虚拟的“专家团队”组成，每个智能体都有明确的职责和分工。

*   **`Coordinator` (协调器)**
    *   **职责**：作为工作流的入口，对用户输入进行初步分析和意图识别。
    *   **决策**：判断问题是否需要启动深度诊断流程。如果问题简单，可以直接回答；如果需要深度诊断，则将其移交给规划流程。
    *   **输出**：决定工作流的下一个走向（直接结束或移交背景调查员）。

*   **`Background Investigator` (背景调查员)**
    *   **职责**：在制定详细计划之前，进行初步的信息搜集和背景调查。
    *   **工具**：主要使用搜索引擎工具。
    *   **输出**：为后续的规划器提供更丰富的上下文信息。

*   **`Planner` (规划器)**
    *   **职责**：整个诊断流程的“大脑”，负责制定结构化的、分步骤的诊断计划。
    *   **输入**：用户问题、背景调查信息、可选的SOP（标准操作程序）。
    *   **输出**：一个 JSON 格式的、包含多个步骤（Step）的诊断计划（Plan）。

*   **`Human Feedback` (人工反馈)**
    *   **职责**：一个关键的人机交互节点，将规划器生成的计划呈现给人类专家进行审核。
    *   **决策**：人类专家可以批准计划、拒绝计划或提出修改意见。
    *   **输出**：专家的决策，决定是按计划执行还是返回规划器重新规划。

*   **`Research Team` (研究团队)**
    *   **职责**：一个“主管”或“调度器”智能体，负责执行已批准的计划。
    *   **决策**：它会遍历计划中的每一个步骤，并根据步骤的类型（`research` 或 `processing`）将其分发给相应的执行者。
    *   **输出**：将任务分派给 `Researcher` 或 `Coder`。

*   **`Researcher` (研究员)**
    *   **职责**：执行信息搜集和调研类型的任务。
    *   **工具**：主要使用 MCP 工具集和搜索引擎。
    *   **输出**：原始的、未加工的诊断数据和信息。

*   **`Coder` (编程员)**
    *   **职责**：执行数据处理和代码分析类型的任务。
    *   **工具**：主要使用 Python REPL（代码执行器）。
    *   **输出**：经过处理和分析后的结构化数据或结论。

*   **`Reporter` (报告员)**
    *   **职责**：在所有诊断步骤完成后，汇总所有收集到的信息和分析结果。
    *   **输入**：整个工作流的状态，包括所有智能体的执行结果。
    *   **输出**：一份结构清晰、内容详实的最终诊断报告。

## 4 状态管理

整个工作流共享一个统一的 **`State`** 对象。这个对象以字典形式存在，包含了工作流执行过程中的所有数据，例如：

*   `messages`: 对话历史
*   `plan`: 当前的诊断计划
*   `observations`: 每个步骤的观察和执行结果
*   `final_report`: 最终报告

每个智能体在执行时都会读取这个 `State` 对象，并在执行完毕后，将其产出更新回 `State` 对象中，从而实现了信息的共享和传递。