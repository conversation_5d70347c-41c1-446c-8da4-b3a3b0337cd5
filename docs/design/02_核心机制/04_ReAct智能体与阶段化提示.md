# ReAct 智能体与阶段化提示

## 目录
- [1 核心问题与解决方案](#1-核心问题与解决方案)
  - [1.1 待解决的问题](#11-待解决的问题)
  - [1.2 解决方案：阶段化提示（Phase-aware Prompting）](#12-解决方案：阶段化提示（phase-aware-prompting）)
- [2 相位判定策略（核心算法）](#2-相位判定策略（核心算法）)
  - [2.1 数据结构定义](#21-数据结构定义)
  - [2.2 相位判定算法](#22-相位判定算法)
  - [2.3 空数据判定规则](#23-空数据判定规则)
- [3 分阶段 Prompt 策略](#3-分阶段-prompt-策略)
  - [3.1 SELECT 阶段：工具选择与调用](#31-select-阶段：工具选择与调用)
  - [3.2 FINAL 阶段：结果分析与答复](#32-final-阶段：结果分析与答复)
- [4 工具输出规范化](#4-工具输出规范化)
  - [4.1 问题背景](#41-问题背景)
  - [4.2 解决方案](#42-解决方案)
- [5 实现架构](#5-实现架构)
  - [5.1 核心文件结构](#51-核心文件结构)
  - [5.2 调用流程](#52-调用流程)
- [6 设计优势与未来扩展](#6-设计优势与未来扩展)
  - [6.1 核心优势](#61-核心优势)
  - [6.2 未来扩展方向](#62-未来扩展方向)


本文档深入阐述了 `backend/src/deep_diagnose/core/reasoning/agents/utils.py` 中实现的核心设计理念：**阶段化提示策略（Phase-aware Prompting）**。该策略旨在解决大型语言模型（LLM）Agent 在复杂工具调用场景中面临的关键挑战，通过模拟人类的“观察-思考-行动”循环，提升Agent的决策效率和响应质量。

以下是 ReAct Agent 阶段化提示设计的核心架构图：

![ReAct Agent 阶段化提示设计](../../assets/png/17_agent_phase_prompting.png)

## 1 核心问题与解决方案

### 1.1 待解决的问题

在 LLM Agent 的实际应用中，我们观察到以下常见问题：

-   **工具滥用**：Agent 在已经获取足够信息或无需进一步工具协助时，仍倾向于不必要地调用工具，导致资源浪费和效率低下。
-   **空数据编造**：当工具返回空结果（如查询无数据）时，Agent 容易“编造”不存在的答案，而非承认信息缺失或采取进一步行动。
-   **阶段混淆**：单一的提示词（Prompt）同时承载“选择工具”和“给出最终结论”的双重任务，导致 Agent 行为模式不稳定，难以预测。

### 1.2 解决方案：阶段化提示（Phase-aware Prompting）

为了解决上述问题，我们引入了**阶段化提示**策略，其核心流程如下：

```
Agent State → Phase Detection → Specialized Prompt → LLM Response
                    ↓
        SELECT Phase ←→ FINAL Phase
        (Tool Calling)   (Final Answer)
```

**核心设计原则**：

1.  **明确阶段分离**：将 Agent 的工作流清晰地划分为不同的阶段，例如“工具选择与调用阶段”（SELECT）和“结果分析与答复阶段”（FINAL）。
2.  **智能相位判定**：Agent 根据当前任务状态和最新工具消息（ToolMessage）的反馈，自动、智能地切换到最合适的处理阶段。
3.  **空数据处理**：特别设计机制来识别并合理处理工具返回的空结果，避免 Agent 产生误导性信息。

## 2 相位判定策略（核心算法）

### 2.1 数据结构定义

为了支持阶段化判定，我们定义了以下关键数据结构：

```python
class Phase(str, Enum):
    SELECT = "select"  # 工具选择与调用阶段
    FINAL = "final"    # 结果分析与答复阶段

@dataclass
class PhaseDecision:
    phase: Phase
    last_tool_output_empty: bool = False    # 最近工具结果是否全为空
    last_tool_empty_count: int = 0          # 空结果数量
```

### 2.2 相位判定算法

**输入**：AgentState 中的 `messages` 列表（包含完整的消息历史）
**输出**：`PhaseDecision` 结构，指示当前 Agent 应处于的阶段及其相关状态。

**判定逻辑**：

```
Step 1: 获取消息尾部连续的 ToolMessage
        ↓
Step 2: 判定逻辑
        ├── 无 ToolMessage → SELECT 阶段 (Agent 需开始或继续选择工具)
        ├── 全部为空结果 → SELECT 阶段 + last_tool_output_empty=True (工具返回空，需重新思考或尝试)
        └── 存在非空结果 → FINAL 阶段 (工具已提供有效信息，可进行最终答复)
```

**关键设计决策**：

-   **只关注尾部连续的 ToolMessage**：这使得算法能够有效支持并发工具调用场景，即 Agent 可以同时执行多个工具，并根据所有工具的最新反馈进行阶段判定。
-   **全空结果视为“需要重新选择工具”**：当所有并发工具调用都返回空数据时，我们认为 Agent 尚未获得有效信息，需要回到 SELECT 阶段重新规划或尝试。
-   通过 `last_tool_output_empty` 和 `last_tool_empty_count` 标记，可以**引导模型调整参数或选择替代工具**，而非盲目重试。

### 2.3 空数据判定规则

为了准确判断工具返回是否为空，我们实现了灵活的空数据判定规则：

```python
def _is_empty_tool_payload(obj) -> bool:
    # 支持常见的空数据模式：
    # - null/None
    # - 空字符串
    # - 空集合 []/{}/()
    # - 包含空数据的复杂结构，如 {"data": null/[]}, {"items": []}, {"result": ""} 等
```

## 3 分阶段 Prompt 策略

根据相位判定结果，Agent 会加载并使用针对特定阶段优化的提示模板。

### 3.1 SELECT 阶段：工具选择与调用

**模板**：`{agent_type}_select.md`

**注入变量**：

```python
{
    "mcp_servers_description": "按 SOP 过滤的可用工具清单",
    "last_tool_output_empty": bool,  # 上一轮工具调用是否全部返回空结果
    "last_tool_empty_count": int     # 连续空结果的次数
}
```

**Prompt 策略**：

-   **强调参数完整性**：提示词会明确要求 Agent 在调用工具时，必须确保所有必要参数（如实例ID、时间范围）都已明确。
-   **推荐并发调用**：鼓励 Agent 在可能的情况下，对多个独立的查询进行并发工具调用，以提高效率。
-   **空结果处理**：若 `last_tool_output_empty=True`，提示词会优先引导 Agent 修正工具参数、尝试不同的工具，或采取其他策略来获取有效信息。

### 3.2 FINAL 阶段：结果分析与答复

**模板**：`{agent_type}_final.md`

**注入变量**：

```python
{
    "recent_tool_evidence": "最近工具输出的摘要（包含 [EMPTY] 标记以指示空结果）",
    "has_empty_tool_output": bool,    # 整个对话过程中是否存在空结果
    "empty_tool_output_count": int,   # 整个对话过程中空结果的总次数
    "locale": "zh-CN"                 # 期望的输出语言
}
```

**Prompt 策略**：

-   **结论先行**：提示词引导 Agent 首先给出明确的最终结论，然后再展开支持该结论的证据链。
-   **空数据约束**：强制 Agent 在答复中说明哪些工具返回了空数据，并解释可能的原因，避免信息缺失被忽略。
-   **工具禁用**：明确指示 Agent 在此阶段“不再调用任何工具”，确保其专注于生成最终答复。

## 4 工具输出规范化

### 4.1 问题背景

LangChain 框架的 `response_format="content_and_artifact"` 要求工具返回数组格式。然而，实际的 MCP 工具可能返回多种格式的数据，例如：

-   `None`
-   单个字符串
-   已格式化的列表

这种不一致性可能导致 LangChain 的输出验证错误，如 `Output validation error: None is not of type 'array'`。

### 4.2 解决方案

为了解决工具输出格式不一致的问题，我们在 `backend/src/deep_diagnose/common/utils/langchain_tool_utils.py` 中实现了工具输出的归一化处理：

```python
# 归一化策略
text_result = None | str | list[str] → list[str]
artifacts_result = None | obj | list[obj] → list[obj]
```

**效果**：通过统一的归一化处理，我们彻底避免了 `Output validation error`，确保了工具输出的稳定性和兼容性。

## 5 实现架构

### 5.1 核心文件结构

以下是实现阶段化提示策略的关键文件及其职责：

```
backend/src/deep_diagnose/core/reasoning/agents/utils.py
├── create_agent_with_tools()        # Agent 工厂函数，负责 Agent 的创建与配置
├── Phase + PhaseDecision            # 定义阶段枚举和阶段判定结果数据结构
├── _detect_phase()                  # 核心相位判定算法实现
├── _analyze_tool_messages()         # 工具消息分析，提取工具执行证据
└── _is_empty_tool_payload()         # 空数据判定辅助函数

backend/src/deep_diagnose/prompts/
├── researcher_select.md             # 研究员 Agent 的工具选择阶段提示模板
└── researcher_final.md              # 研究员 Agent 的最终答复阶段提示模板
```

### 5.2 调用流程

ReAct Agent 的调用流程如下：

```
StepExecutor
    → create_agent_with_tools()       # 创建 Agent 实例
    → generate_prompt(agent_state)    # 根据当前 AgentState 生成动态提示
        → _detect_phase()             # 判定当前 Agent 处于哪个阶段
        → 选择对应阶段的提示模板 + 注入变量 # 根据阶段选择模板并填充动态变量
        → apply_prompt_template()     # 应用提示模板，生成最终提示
    → create_react_agent(prompt=generate_prompt) # 使用生成的提示创建 ReAct Agent
```

## 6 设计优势与未来扩展

### 6.1 核心优势

1.  **可预测性**：明确的阶段转换逻辑使得 Agent 的行为更具可预测性，便于调试和问题排查。
2.  **可扩展性**：新增 Agent 类型时，只需提供对应的 `_select.md` 和 `_final.md` 提示模板，即可快速集成。
3.  **空数据友好**：系统性地处理工具返回的空结果，避免了 Agent 在信息缺失时产生错误或误导性输出。
4.  **效率提升**：通过阶段分离，Agent 能够更专注于当前任务，避免不必要的工具调用，从而提升整体效率。

### 6.2 未来扩展方向

-   **多阶段细化**：可以进一步细化阶段，例如引入 `ANALYZE`（分析工具结果）、`RE-SELECT`（重新选择工具）等中间阶段，以支持更复杂的推理链。
-   **工具禁用**：在 `FINAL` 阶段，可以考虑通过 LangChain 的 `tool_choice=none` 参数硬性禁用工具调用，确保 Agent 专注于生成最终答复。
-   **模块重构**：将 `utils.py` 拆分为更小的、职责单一的模块，例如 `agent_factory.py` 和 `phase_manager.py`，以提高代码的可维护性和可读性。