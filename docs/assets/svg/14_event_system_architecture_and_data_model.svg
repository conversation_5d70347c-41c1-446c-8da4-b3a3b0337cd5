<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1050" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1600 1050">
  <!-- Background -->
  <rect width="100%" height="100%" fill="#ffffff"/>
  <defs>
    <!-- v4配色方案: 板岩与琥珀 (Slate & Amber) -->
    <linearGradient id="eventGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#0284c7;" /><stop offset="100%" style="stop-color:#38bdf8;" /></linearGradient>
    <linearGradient id="collectorGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#6d28d9;" /><stop offset="100%" style="stop-color:#8b5cf6;" /></linearGradient>
    <linearGradient id="storageGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#475569;" /><stop offset="100%" style="stop-color:#64748b;" /></linearGradient>
    <linearGradient id="processorGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#0d9488;" /><stop offset="100%" style="stop-color:#2dd4bf;" /></linearGradient>
    <linearGradient id="modelGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f59e0b;" /><stop offset="100%" style="stop-color:#fbbf24;" /></linearGradient>
    
    <!-- 箭头和标记 -->
    <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse"><path d="M 0 0 L 10 5 L 0 10 z" fill="#1e293b" /></marker>
    <marker id="inheritance" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse"><path d="M 0 0 L 10 5 L 0 10 L 1 5 z" fill="none" stroke="#0d9488" stroke-width="2"/></marker>
    
    <!-- 样式 -->
    <style>
      .main-title { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 32px; font-weight: 700; fill: #0f172a; }
      .header-title { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 20px; font-weight: 600; fill: #334155; }
      .layer-title { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 18px; font-weight: 600; fill: #1e293b; text-anchor: middle; }
      .subtitle { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .text { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 12px; fill: #ffffff; }
      .small-text { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 11px; fill: #475569; }
      
      .highlight-card { fill: #fffbeb; stroke: #fde68a; stroke-width: 1; }
      .highlight-title { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 15px; font-weight: bold; fill: #be123c; }
      .highlight-text { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; font-size: 12px; fill: #334155; line-height: 1.5; }
      
      .box { filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.12)); stroke-width: 1.5; }
      .event-box { fill: url(#eventGradient); stroke: #0284c7; }
      .collector-box { fill: url(#collectorGradient); stroke: #6d28d9; }
      .storage-box { fill: url(#storageGradient); stroke: #475569; }
      .processor-box { fill: url(#processorGradient); stroke: #0d9488; }
      .model-box { fill: url(#modelGradient); stroke: #f59e0b; }
      
      .flow-arrow { stroke: #334155; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
      .inheritance-line { stroke: #0d9488; stroke-width: 2; fill: none; stroke-dasharray: 6 3; marker-end: url(#inheritance); }
      .composition-line { stroke: #6d28d9; stroke-width: 2; fill: none; stroke-dasharray: 4 2; }
    </style>
  </defs>

  <!-- 1. 主标题 -->
  <text x="800" y="50" text-anchor="middle" class="main-title">ECS深度诊断系统：Langgraph Message处理流程</text>

  <!-- 2. 核心价值亮点 -->
  <g id="HighlightsHeader" transform="translate(0, 80)">
    <text x="800" y="20" text-anchor="middle" class="header-title">核心架构价值</text>
    <rect x="50" y="50" width="1500" height="2" fill="#e2e8f0"/>
    
    <g id="Highlight1_Maintainability">
        <rect x="50" y="70" width="480" height="100" class="highlight-card" rx="8"/>
        <text x="70" y="95" class="highlight-title">高可维护性：清晰的关注点分离 (SoC)</text>
        <text x="70" y="120" class="highlight-text">数据层(存什么)与处理层(怎么做)完全解耦。这使得团队可以</text>
        <text x="70" y="140" class="highlight-text">独立开发和迭代，极大简化了维护成本和认知负担。</text>
    </g>
    <g id="Highlight2_Extensibility">
        <rect x="560" y="70" width="480" height="100" class="highlight-card" rx="8"/>
        <text x="580" y="95" class="highlight-title">高可扩展性：基于接口的策略模式</text>
        <text x="580" y="120" class="highlight-text">新增Agent类型时，仅需添加新的处理器实现，无需修改任何</text>
        <text x="580" y="140" class="highlight-text">核心逻辑。架构具备“插件式”能力，可快速响应业务变化。</text>
    </g>
    <g id="Highlight3_Reliability">
        <rect x="1070" y="70" width="480" height="100" class="highlight-card" rx="8"/>
        <text x="1090" y="95" class="highlight-title">高可靠性：统一的范式数据模型</text>
        <text x="1090" y="120" class="highlight-text">`AgentMessage` 作为流程中唯一的“真相来源”(Source of Truth)，</text>
        <text x="1090" y="140" class="highlight-text">确保了数据的一致性，从根本上提升了系统的稳定与可预测性。</text>
    </g>
  </g>

  <!-- 3. 主架构图 -->
  <g id="MainArchitecture" transform="translate(0, 240)">
    
    <!-- 列1: 输入与收集 -->
    <g id="Layer1_Collection">
      <text x="215" y="50" class="layer-title">1. 输入与收集</text>
      <rect x="60" y="70" width="310" height="520" fill="#f8fafc" rx="10" />
      
      <g id="ExternalInputs">
        <rect x="80" y="90" width="270" height="130" class="collector-box box" rx="8"/>
        <text x="215" y="115" text-anchor="middle" class="subtitle">外部事件源</text>
        <text x="215" y="145" text-anchor="middle" class="text">AIMessageChunk</text>
        <text x="215" y="165" text-anchor="middle" class="text">ToolMessage</text>
        <text x="215" y="185" text-anchor="middle" class="text">其他消息类型...</text>
      </g>
      
      <path d="M 215 220 Q 215 245 215 270" class="flow-arrow"/>
      
      <g id="EventCoordinator">
        <rect x="80" y="270" width="270" height="80" class="event-box box" rx="8"/>
        <text x="215" y="295" text-anchor="middle" class="subtitle">ReasoningAgentEventV2</text>
        <text x="215" y="315" text-anchor="middle" class="text">事件入口，协调处理流程</text>
        <text x="215" y="335" text-anchor="middle" class="text">维护核心业务状态</text>
      </g>
      
      <path d="M 215 350 Q 215 375 215 400" class="flow-arrow"/>

      <g id="MessageCollector">
        <rect x="80" y="400" width="270" height="80" class="collector-box box" rx="8"/>
        <text x="215" y="425" text-anchor="middle" class="subtitle">MessageCollector</text>
        <text x="215" y="445" text-anchor="middle" class="text">收集与预处理原始消息</text>
      </g>
    </g>

    <!-- 列2: 数据存储 -->
    <g id="Layer2_Storage">
      <text x="605" y="50" class="layer-title">2. 统一数据存储</text>
      <rect x="450" y="70" width="310" height="520" fill="#f8fafc" rx="10" />

      <path d="M 370 440 Q 410 440 450 440" class="flow-arrow"/>

      <g id="MessageRepository">
        <rect x="470" y="90" width="270" height="80" class="storage-box box" rx="8"/>
        <text x="605" y="115" text-anchor="middle" class="subtitle">MessageRepository</text>
        <text x="605" y="135" text-anchor="middle" class="text">提供统一、安全的数据访问接口</text>
      </g>
      
      <path d="M 605 170 Q 605 195 605 220" class="composition-line"/>
      <text x="615" y="195" class="small-text">manages</text>
      
      <g id="DataModels">
          <rect x="470" y="220" width="270" height="110" class="model-box box" rx="8"/>
          <text x="605" y="245" text-anchor="middle" class="subtitle">AgentMessage</text>
          <text x="485" y="270" class="text">• run_id, agent, is_finished</text>
          <text x="485" y="290" class="text">• tool_executions: List[ToolExecution]</text>
          
          <rect x="470" y="360" width="270" height="90" class="model-box box" rx="8"/>
          <text x="605" y="385" text-anchor="middle" class="subtitle">ToolExecution</text>
          <text x="485" y="410" class="text">• call_id, call_name, status</text>
          <text x="485" y="430" class="text">• call_args, call_result</text>
      </g>
    </g>
    
    <!-- 列3: 业务处理 -->
    <g id="Layer3_Processing">
      <text x="1105" y="50" class="layer-title">3. 业务逻辑处理</text>
      <rect x="850" y="70" width="410" height="660" fill="#f8fafc" rx="10"/>

      <!-- *** FIX: Corrected path syntax *** -->
      <path d="M 760 290 Q 805 290 850 290" class="flow-arrow"/>
      <text x="805" y="285" class="small-text">reads from</text>

      <g id="BaseProcessor">
        <rect x="870" y="90" width="370" height="70" class="processor-box box" rx="8" stroke-dasharray="8 4"/>
        <text x="1055" y="115" text-anchor="middle" class="subtitle">BaseMessageProcessor</text>
        <text x="1055" y="140" text-anchor="middle" class="text">+ process_messages()  |  + get_agent_name()</text>
      </g>
      
      <g id="ConcreteProcessors">
        <rect x="870" y="210" width="370" height="90" class="processor-box box" rx="8"/>
        <text x="1055" y="235" text-anchor="middle" class="subtitle">PlannerProcessor</text>
        <text x="1055" y="260" text-anchor="middle" class="text">解析思考过程，提取计划步骤</text>
        
        <rect x="870" y="320" width="370" height="90" class="processor-box box" rx="8"/>
        <text x="1055" y="345" text-anchor="middle" class="subtitle">ResearcherProcessor</text>
        <text x="1055" y="370" text-anchor="middle" class="text">处理工具调用，聚合参数与结果</text>

        <rect x="870" y="430" width="370" height="90" class="processor-box box" rx="8"/>
        <text x="1055" y="455" text-anchor="middle" class="subtitle">ReporterProcessor</text>
        <text x="1055" y="480" text-anchor="middle" class="text">收集并聚合最终的思考与输出</text>
        
        <rect x="870" y="540" width="370" height="70" class="processor-box box" rx="8" stroke-dasharray="4 4" opacity="0.8"/>
        <text x="1055" y="575" text-anchor="middle" class="text">New_Agent_Processor...</text>
      </g>

      <!-- Inheritance Lines -->
      <path d="M 950 210 L 950 160" class="inheritance-line"/>
      <path d="M 1055 210 L 1055 160" class="inheritance-line"/>
      <path d="M 1160 210 L 1160 160" class="inheritance-line"/>
      <text x="960" y="185" class="small-text" transform="rotate(90 960 185)">implements</text>
    </g>
  </g>
</svg>