<svg width="1800" height="1100" viewBox="0 0 1800 1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 更现代的渐变配色 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fafbff"/>
      <stop offset="100%" style="stop-color:#f0f4f8"/>
    </linearGradient>
    
    <!-- 输入模块 - 蓝色系 -->
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5"/>
      <stop offset="100%" style="stop-color:#3730a3"/>
    </linearGradient>
    
    <!-- 处理流水线 - 青绿色系 -->
    <linearGradient id="pipelineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669"/>
      <stop offset="100%" style="stop-color:#047857"/>
    </linearGradient>
    
    <!-- 输出模块 - 紫色系 -->
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed"/>
      <stop offset="100%" style="stop-color:#5b21b6"/>
    </linearGradient>
    
    <!-- 新增强调色渐变 -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b"/>
      <stop offset="100%" style="stop-color:#d97706"/>
    </linearGradient>
    
    <!-- 优化阴影效果 -->
    <filter id="dropShadow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#0f172a" flood-opacity="0.08"/>
    </filter>
    
    <!-- 强化阴影用于重要组件 -->
    <filter id="strongShadow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="12" stdDeviation="24" flood-color="#0f172a" flood-opacity="0.12"/>
    </filter>
    
    <!-- 优化箭头样式 -->
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#374151"/>
    </marker>
    
    <!-- 白色箭头用于深色背景 -->
    <marker id="whiteArrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ffffff"/>
    </marker>
    
    <style>
      /* 主标题 - 增强层次 */
      .title { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 36px; 
        font-weight: 800; 
        fill: #0f172a; 
        text-anchor: middle;
        letter-spacing: -0.5px;
      }
      
      /* 层级标题 - 优化对比度 */
      .layer-title { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 24px; 
        font-weight: 700; 
        fill: #ffffff; 
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }
      
      /* 盒子标题 - 提升可读性 */
      .box-title { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 16px; 
        font-weight: 600; 
        fill: #1e293b; 
        letter-spacing: -0.2px;
      }
      
      /* 正文 - 优化颜色对比 */
      .text { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 14px; 
        fill: #475569; 
        line-height: 1.4;
      }
      
      /* 等宽字体 - 代码样式 */
      .mono { 
        font-family: 'SF Mono', 'Fira Code', 'Monaco', 'Cascadia Code', monospace; 
        font-size: 13px; 
        fill: #374151; 
        font-weight: 500;
      }
      
      /* 箭头样式 */
      .arrow { 
        stroke: #374151; 
        stroke-width: 3; 
        fill: none; 
        marker-end: url(#arrowhead);
        opacity: 0.8;
      }
      
      .white-arrow {
        stroke: #ffffff;
        stroke-width: 3;
        fill: none;
        marker-end: url(#whiteArrowhead);
        opacity: 0.9;
      }
      
      /* 面板样式 - 增强立体感 */
      .panel { 
        fill: #ffffff; 
        stroke: #cbd5e1; 
        stroke-width: 2; 
        filter: url(#dropShadow); 
        rx: 16; 
      }
      
      .main-panel {
        fill: #ffffff;
        stroke: #94a3b8;
        stroke-width: 2;
        filter: url(#strongShadow);
        rx: 20;
      }
      
      /* 子面板 - 优化透明度 */
      .sub-panel { 
        fill: rgba(255,255,255,0.95); 
        stroke: rgba(255,255,255,0.8); 
        stroke-width: 1.5; 
        rx: 12;
        filter: url(#dropShadow);
      }
      
      /* 数据高亮 */
      .data { 
        font-family: 'SF Mono', 'Fira Code', 'Monaco', monospace; 
        font-size: 13px; 
        font-weight: 600; 
        fill: #047857;
        background: rgba(16, 185, 129, 0.1);
      }
      
      /* 高亮区域样式 */
      .highlight-title { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 18px; 
        font-weight: 700; 
        fill: #0f172a;
        letter-spacing: -0.2px;
      }
      
      .highlight-text { 
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
        font-size: 15px; 
        fill: #475569;
        line-height: 1.5;
      }
      
      /* 新增样式类 */
      .accent-box {
        fill: rgba(245, 158, 11, 0.1);
        stroke: #f59e0b;
        stroke-width: 2;
        rx: 8;
      }
      
      .process-box {
        fill: rgba(16, 185, 129, 0.05);
        stroke: #10b981;
        stroke-width: 2;
        rx: 10;
      }
    </style>
  </defs>

  <rect width="1800" height="1100" fill="url(#bgGradient)"/>
  <text x="900" y="60" class="title">事件处理流水线架构</text>

  <!-- 优化的核心要点展示区域 -->
  <g transform="translate(100, 100)">
    <rect width="1600" height="120" class="main-panel"/>
    
    <!-- 左侧核心点 -->
    <g transform="translate(60, 35)">
      <circle cx="0" cy="10" r="8" fill="#f59e0b"/>
      <text x="25" class="highlight-title">核心瓶颈解耦: IO与CPU分离</text>
      <text x="25" y="28" class="highlight-text">事件接收(IO密集)与处理(CPU密集)通过异步队列分离，消除阻塞，</text>
      <text x="25" y="48" class="highlight-text">提升系统吞吐量与实时响应速度。</text>
    </g>
    
    <!-- 右侧核心点 -->
    <g transform="translate(850, 35)">
      <circle cx="0" cy="10" r="8" fill="#10b981"/>
      <text x="25" class="highlight-title">分层确定性加工: 原始事件 → 标准化 → 业务洞察</text>
      <text x="25" y="28" class="highlight-text">流水线分层设计: Collector归一化，Repository提供稳定状态，</text>
      <text x="25" y="48" class="highlight-text">Processor提炼业务价值，每层职责单一。</text>
    </g>
  </g>

  <!-- 主要流程图 -->
  <g transform="translate(50, 260)">
    <!-- 输入模块 - 增强样式 -->
    <rect x="0" y="0" width="420" height="520" fill="url(#inputGradient)" rx="20" class="main-panel"/>
    <text x="40" y="45" class="layer-title">🔄 输入模块 (IO密集)</text>
    
    <!-- LangGraph 原始事件 -->
    <g transform="translate(60, 110)">
      <rect width="300" height="110" class="sub-panel"/>
      <text x="150" y="35" text-anchor="middle" class="box-title">LangGraph 原始事件</text>
      <text x="150" y="60" text-anchor="middle" class="mono">(message_chunk, metadata)</text>
      <text x="150" y="85" text-anchor="middle" class="text" style="fill:#7c2d12; font-size:12px;">高频实时数据流</text>
    </g>
    
    <!-- 流向箭头 -->
    <path d="M 210 220 V 270" class="white-arrow"/>
    
    <!-- ReasoningAgentEvent 处理 -->
    <g transform="translate(60, 270)">
      <rect width="300" height="120" class="sub-panel"/>
      <text x="150" y="30" text-anchor="middle" class="box-title">ReasoningAgentEvent</text>
      <text x="150" y="55" text-anchor="middle" class="mono">.handle_message()</text>
      <text x="150" y="80" text-anchor="middle" class="text">快速放入队列</text>
      <text x="150" y="100" text-anchor="middle" class="text" style="fill:#7c2d12; font-size:12px;">⚡ 非阻塞处理</text>
    </g>
    
    <!-- 向右的箭头到流水线 -->
    <path d="M 420 330 H 480" class="arrow"/>

    <!-- 处理流水线模块 - 增强布局 -->
    <g transform="translate(480, 0)">
      <rect width="1270" height="870" fill="url(#pipelineGradient)" rx="20" class="main-panel"/>
      <text x="40" y="45" class="layer-title">⚙️ 后台处理流水线 (CPU密集)</text>
      
      <!-- 异步队列 - 核心缓冲区 -->
      <g transform="translate(60, 110)">
        <rect width="220" height="120" class="sub-panel" style="stroke:#f59e0b; stroke-width:3;"/>
        <text x="110" y="35" text-anchor="middle" class="box-title">asyncio.Queue</text>
        <text x="110" y="60" text-anchor="middle" class="text">核心解耦缓冲</text>
        <text x="110" y="85" text-anchor="middle" class="text" style="fill:#d97706; font-size:12px; font-weight:600;">🔗 解耦关键</text>
      </g>
      
      <!-- 向右流动箭头 -->
      <path d="M 280 170 H 340" class="white-arrow"/>

      <!-- 第1层：消息收集器 -->
      <g transform="translate(340, 90)">
        <rect width="320" height="170" class="process-box"/>
        <text x="25" y="30" class="box-title">📥 1. 收集器: MessageCollector</text>
        <text x="25" y="55" class="mono">.collect_message()</text>
        <text x="25" y="85" class="highlight-title" style="font-size:14px; fill:#047857;">核心功能:</text>
        <text x="35" y="110" class="text" style="font-size:13px;">将异构、零散的原始事件块</text>
        <text x="35" y="130" class="text" style="font-size:13px;">(AIMessageChunk)归一化处理</text>
        <text x="35" y="150" class="text" style="font-size:12px; fill:#059669; font-weight:600;">→ 统一数据格式</text>
      </g>
      
      <!-- 向下流动 -->
      <path d="M 500 260 V 320" class="white-arrow"/>
      <text x="520" y="295" class="text" style="fill:white; font-size:13px; font-weight:600;">📝 更新/创建统一模型</text>

      <!-- 第2层：消息仓库 -->
      <g transform="translate(340, 320)">
        <rect width="320" height="200" class="sub-panel" style="stroke-width:3; stroke:#10b981;"/>
        <text x="25" y="30" class="box-title">🗄️ 2. 仓库: MessageRepository</text>
        <text x="25" y="60" class="text" style="font-weight:600;">内存中的单一事实源 (State Store)</text>
        <text x="25" y="90" class="mono">核心数据模型: <tspan class="data">AgentMessage</tspan></text>
        <text x="25" y="125" class="mono" style="font-weight:600;">提供高效索引:</text>
        <text x="35" y="150" class="data" style="font-size:14px;">• run_id</text>
        <text x="35" y="170" class="data" style="font-size:14px;">• agent_name</text>
        <text x="160" y="150" class="data" style="font-size:14px;">• timestamp</text>
        <text x="160" y="170" class="data" style="font-size:14px;">• event_type</text>
      </g>
      
      <!-- 向右查询 -->
      <path d="M 660 420 H 720" class="white-arrow"/>
      <text x="670" y="410" class="text" style="fill:white; font-size:13px; font-weight:600;">🔍 按需查询</text>

      <!-- 第3层：业务处理器集群 -->
      <g transform="translate(720, 90)">
        <rect width="500" height="620" class="sub-panel" style="border: 2px solid #3730a3;"/>
        <text x="25" y="30" class="box-title">⚡ 3. 处理器: BusinessMessageProcessor</text>
        <text x="25" y="55" class="mono">.process_business_messages()</text>
        <text x="25" y="80" class="text" style="font-weight:600;">根据 agent_name 委托给子处理器</text>
        
        <!-- Planner 处理器 -->
        <g transform="translate(25, 110)">
          <rect width="450" height="140" rx="12" fill="#eff6ff" stroke="#3b82f6" stroke-width="2"/>
          <text x="20" y="25" class="box-title" style="font-size:15px; fill:#1d4ed8;">🎯 PlannerMessageProcessor</text>
          <text x="20" y="50" class="text">输入: Planner 的 AgentMessage</text>
          <text x="20" y="75" class="text">输出: <tspan class="data" style="font-size:13px;">{"thought", "plan_steps"}</tspan></text>
          <text x="20" y="100" class="text" style="font-size:12px; fill:#1d4ed8;">🔍 提取规划思路和执行步骤</text>
          <text x="20" y="120" class="text" style="font-size:12px; fill:#1d4ed8;">📋 结构化计划数据</text>
        </g>
        
        <!-- Researcher 处理器 -->
        <g transform="translate(25, 270)">
          <rect width="450" height="140" rx="12" fill="#f0fdf4" stroke="#16a34a" stroke-width="2"/>
          <text x="20" y="25" class="box-title" style="font-size:15px; fill:#15803d;">🔬 ResearcherMessageProcessor</text>
          <text x="20" y="50" class="text">输入: Researcher 的 AgentMessage</text>
          <text x="20" y="75" class="text">输出: <tspan class="data" style="font-size:13px;">{"executions", "results"}</tspan></text>
          <text x="20" y="100" class="text" style="font-size:12px; fill:#15803d;">🔍 解析执行结果和数据</text>
          <text x="20" y="120" class="text" style="font-size:12px; fill:#15803d;">📊 提取关键洞察</text>
        </g>
        
        <!-- 扩展区域 -->
        <g transform="translate(25, 430)">
          <rect width="450" height="170" rx="12" fill="#fefce8" stroke="#ca8a04" stroke-width="2"/>
          <text x="20" y="25" class="box-title" style="font-size:15px; fill:#a16207;">🔧 插件化扩展架构</text>
          <text x="20" y="55" class="highlight-title" style="font-size:13px; fill:#a16207;">实现步骤:</text>
          <text x="35" y="80" class="text" style="font-size:13px;">1. 继承 BaseMessageProcessor</text>
          <text x="35" y="100" class="text" style="font-size:13px;">2. 实现 process() 方法</text>
          <text x="35" y="120" class="text" style="font-size:13px;">3. 在 BusinessProcessor 中注册</text>
          <text x="35" y="145" class="text" style="font-size:12px; fill:#a16207; font-weight:600;">💡 零耦合, 易扩展, 可测试</text>
        </g>
      </g>
      
      <!-- 向下聚合流动 -->
      <path d="M 970 710 V 760" class="white-arrow"/>
      <text x="980" y="740" class="text" style="fill:white; font-size:13px; font-weight:600;">📊 聚合后的业务数据</text>

      <!-- 第4层：应用状态更新 -->
      <g transform="translate(720, 760)">
        <rect width="500" height="90" class="sub-panel" style="stroke:#7c3aed; stroke-width:3;"/>
        <text x="250" y="35" text-anchor="middle" class="box-title">🔄 4. 应用状态同步</text>
        <text x="250" y="60" text-anchor="middle" class="mono">_apply_event_data(processed_data)</text>
        <text x="250" y="80" text-anchor="middle" class="text" style="fill:#7c3aed; font-weight:600;">更新全局事件状态</text>
      </g>
    </g>
    <!-- 输出模块 - 重新设计 -->
    <g transform="translate(500, 850)">
      <!-- 向上的连接箭头 -->
      <path d="M 720 0 V -40" class="arrow"/>
      
      <g transform="translate(0, -280)">
        <rect x="400" y="0" width="450" height="280" fill="url(#outputGradient)" rx="20" class="main-panel"/>
        <text x="430" y="45" class="layer-title">📤 输出模块</text>
        
        <!-- 状态同步区域 -->
        <g transform="translate(450, 80)">
          <rect width="350" height="160" class="sub-panel"/>
          <text x="175" y="30" text-anchor="middle" class="box-title">🔄 更新 ReasoningAgentEvent</text>
          <text x="175" y="55" text-anchor="middle" class="text" style="font-weight:600;">同步公共字段</text>
          <text x="175" y="80" text-anchor="middle" class="mono">(.thought, .result, .status...)</text>
          <text x="175" y="105" text-anchor="middle" class="text" style="fill:#7c3aed;">主循环检测到 _version 变更</text>
          <text x="175" y="130" text-anchor="middle" class="text" style="fill:#7c3aed; font-weight:600;">⚡ 流式推送完整事件</text>
          <text x="175" y="150" text-anchor="middle" class="text" style="font-size:12px; fill:#5b21b6;">📡 实时响应用户界面</text>
        </g>
      </g>
    </g>
  </g>
</svg>