<svg width="1800" height="1100" viewBox="0 0 1800 1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc"/><stop offset="100%" style="stop-color:#f1f5f9"/></linearGradient>
    <linearGradient id="producerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6"/><stop offset="100%" style="stop-color:#7c3aed"/></linearGradient>
    <linearGradient id="consumerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6"/><stop offset="100%" style="stop-color:#1d4ed8"/></linearGradient>
    <linearGradient id="redisGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#ef4444"/><stop offset="100%" style="stop-color:#dc2626"/></linearGradient>
    
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/></filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#475569"/></marker>
    
    <style>
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; text-anchor: middle; }
      .layer-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 22px; font-weight: 600; fill: white; }
      .box-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #475569; }
      .mono { font-family: 'SF Mono', 'Monaco', monospace; font-size: 12px; fill: #334155; }
      .arrow { stroke: #475569; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
      .panel { fill: #ffffff; stroke: #e2e8f0; stroke-width: 1.5; filter: url(#dropShadow); rx: 12; }
      .sub-panel { fill: rgba(255,255,255,0.85); stroke: rgba(255,255,255,0.9); stroke-width:1; rx:8; }
      .highlight-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .highlight-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: #475569; }
    </style>
  </defs>

  <rect width="1800" height="1100" fill="url(#bgGradient)"/>
  <text x="900" y="60" class="title">Redis Pub/Sub 与快照缓存双轨制架构</text>

  <!-- Highlights Section -->
  <g transform="translate(150, 100)">
    <rect width="1500" height="100" class="panel"/>
    <g transform="translate(50, 30)">
      <text class="highlight-title">低延迟与高可靠兼得</text>
      <text x="15" y="25" class="highlight-text">Pub/Sub实现毫秒级实时事件推送；Redis快照提供数据持久化与消费失败后的回退保障。</text>
    </g>
    <g transform="translate(800, 30)">
      <text class="highlight-title">智能降级与统一总线</text>
      <text x="15" y="25" class="highlight-text">消费端优先订阅，异常时无缝切换至轮询快照。`ChatEventBus`封装所有Redis操作，简化上层逻辑。</text>
    </g>
  </g>

  <!-- Main Flow -->
  <g transform="translate(50, 240)">
    <!-- Producer Module -->
    <rect x="0" y="0" width="400" height="600" fill="url(#producerGradient)" rx="16" class="panel"/>
    <text x="30" y="40" class="layer-title">生产者模块</text>
    <text x="30" y="65" class="text" style="fill:white; opacity:0.8;">AsynchronousRequestProcessor</text>
    <g transform="translate(50, 120)">
      <rect width="300" height="120" class="sub-panel"/>
      <text x="150" y="45" text-anchor="middle" class="box-title">后台Agent执行</text>
      <text x="150" y="70" text-anchor="middle" class="mono">_run_agent_in_background()</text>
      <text x="150" y="95" text-anchor="middle" class="text">循环产生 <tspan style="font-weight:600;">BaseAgentOutputEvent</tspan></text>
    </g>
    <path d="M 200 240 V 290" class="arrow" style="stroke:white;"/>
    <g transform="translate(50, 290)">
      <rect width="300" height="220" class="sub-panel"/>
      <text x="150" y="35" text-anchor="middle" class="box-title">调用事件总线</text>
      <text x="20" y="70" class="highlight-title" style="font-size:13px;">实时轨道 (低延迟):</text>
      <text x="35" y="95" class="mono">event_bus.publish(event)</text>
      <text x="35" y="115" class="text" style="font-size:12px;">每条事件都实时发布</text>
      <text x="20" y="155" class="highlight-title" style="font-size:13px;">回放轨道 (高可靠):</text>
      <text x="35" y="180" class="mono">event_bus.write_snapshot(event)</text>
      <text x="35" y="200" class="text" style="font-size:12px;">节流写入 (如每10条)，避免阻塞</text>
    </g>
    <path d="M 400 400 H 450" class="arrow"/>

    <!-- Redis Module -->
    <g transform="translate(450, 0)">
      <rect width="400" height="800" fill="url(#redisGradient)" rx="16" class="panel"/>
      <text x="30" y="40" class="layer-title">基础设施: Redis</text>
      <g transform="translate(50, 200)">
        <rect width="300" height="150" class="sub-panel"/>
        <text x="150" y="45" text-anchor="middle" class="box-title">Pub/Sub Channel</text>
        <text x="150" y="75" text-anchor="middle" class="text">用于实时消息传递</text>
        <text x="150" y="105" text-anchor="middle" class="mono">CHAT_EVENT:channel:{req_id}</text>
      </g>
      <g transform="translate(50, 450)">
        <rect width="300" height="150" class="sub-panel"/>
        <text x="150" y="45" text-anchor="middle" class="box-title">Snapshot Cache Key</text>
        <text x="150" y="75" text-anchor="middle" class="text">用于回放和历史查询</text>
        <text x="150" y="105" text-anchor="middle" class="mono">CHAT_EVENT:{req_id}</text>
        <text x="150" y="125" text-anchor="middle" class="text" style="font-weight:600;">(TTL: 6 hours)</text>
      </g>
    </g>
    <path d="M 850 400 H 900" class="arrow"/>

    <!-- Consumer Module -->
    <g transform="translate(900, 0)">
      <rect width="850" height="600" fill="url(#consumerGradient)" rx="16" class="panel"/>
      <text x="30" y="40" class="layer-title">消费者模块</text>
      <text x="30" y="65" class="text" style="fill:white; opacity:0.8;">AsynchronousRequestProcessor</text>
      
      <g transform="translate(50, 120)">
        <rect width="750" height="400" class="sub-panel"/>
        <text x="375" y="45" text-anchor="middle" class="box-title">智能降级消费逻辑</text>
        
        <g transform="translate(50, 80)">
          <text class="highlight-title">1. 优先路径: 订阅Pub/Sub</text>
          <text x="15" y="25" class="mono">stream = event_bus.subscribe()</text>
          <text x="15" y="45" class="mono">_stream_via_pubsub(stream)</text>
          <text x="15" y="70" class="text">优点: 毫秒级延迟, 实时性好。</text>
          <text x="15" y="90" class="text">处理: 基于`seq`的乱序缓冲, 保证事件有序。</text>
        </g>
        
        <path d="M 375 190 V 230" class="arrow" style="stroke-dasharray: 5 5;"/>
        <text x="385" y="215" class="text" style="font-weight:600;">若订阅或消费异常</text>

        <g transform="translate(50, 230)">
          <text class="highlight-title">2. 降级路径: 轮询快照</text>
          <text x="15" y="25" class="mono">_stream_from_redis()</text>
          <text x="15" y="45" class="mono">event_bus.read_snapshot()</text>
          <text x="15" y="70" class="text">优点: 可靠性高, 保证最终可达。</text>
          <text x="15" y="90" class="text">处理: 低频轮询(100ms), 仅在数据变化时产出。</text>
        </g>
      </g>
    </g>
  </g>
</svg>